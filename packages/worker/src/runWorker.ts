import { getTemporalNativeConnection, PIGBOT_WORKER_QUEUE } from "pigbot-core/src/temporal/temporal";
import { Worker } from "@temporalio/worker";
import * as activities from "./activities";
import Config from "pigbot-core/src/Config";

export async function createWorker() {
	const connection = await getTemporalNativeConnection();

	const workflowOption = () =>
		process.env.NODE_ENV === "production"
			? {
					workflowBundle: {
						codePath: require.resolve("../workflow-bundle.js"),
					},
				}
			: { workflowsPath: require.resolve("./workflows") };

	// Step 2: Register Workflows and Activities with the Worker.
	const worker = await Worker.create({
		connection,
		namespace: Config.TEMPORAL_NAMESPACE,
		taskQueue: PIGBOT_WORKER_QUEUE,
		...workflowOption(),
		bundlerOptions: {
			ignoreModules: ["path", "fs", "os", "crypto"],
		},
		activities,
	});

	return {
		worker,
		connection,
	};
}

export async function runWorker() {
	const { worker } = await createWorker();

	await worker.run();
}
