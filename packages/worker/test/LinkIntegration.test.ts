import "pigbot-core/test/jest.setup";
import { afterAll, beforeAll, beforeEach, expect, test } from "@jest/globals";
import { DockerComposeEnvironment, StartedDockerComposeEnvironment } from "testcontainers";
import path from "path";
import { TestData } from "pigbot-core/test/TestData";
import { NativeConnection, Worker } from "@temporalio/worker";
import { DefaultTestLength } from "pigbot-core/test/jest.setup";
import postgres from "postgres";
import * as process from "node:process";

let environment: StartedDockerComposeEnvironment;

// To test against your changes to cf-pigbot-link, push your link branch and copy the tag of the built docker image.
// Then, replace the tag in the LINK_TAG value below.
const ENV = { LINK_TAG: "507131353af78c10432f3174e111ccfe14548e8b" };

/**
 * Stream logs from a container to the console
 */
async function streamContainerLogs(containerName: string) {
	const container = environment.getContainer(containerName);
	const logStream = await container.logs();

	// Set up event handlers for the log stream
	logStream.on("data", (line) => {
		process.stdout.write(`[${containerName}] ${line}`);
	});

	logStream.on("err", (line) => {
		process.stderr.write(`[${containerName}] ERROR: ${line}`);
	});

	return logStream;
}

beforeAll(
	async () => {
		environment = await new DockerComposeEnvironment(path.resolve(__dirname, "./link-deps"), "docker-compose.yml")
			.withEnvironment(ENV)
			.up();

		const temporal = environment.getContainer("temporal-1");

		process.env.TEMPORAL_ADDRESS = `${temporal.getHost()}:${temporal.getMappedPort(7233)}`;
		process.env.TEMPORAL_NAMESPACE = "default";
		process.env.TEMPORAL_JWT_TOKEN =
			"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2Vfcm9sZXMiOnsiZGVmYXVsdCI6ImFkbWluIn19.XxHHJGZd-6RwR_k1zXwVmrIPbxGvbLT_kEbpuv42Gf4";

		const ui = environment.getContainer("temporal-ui-1");
		// eslint-disable-next-line no-console
		console.log(`Temporal UI: ${ui.getHost()}:${ui.getMappedPort(8080)}`);

		await streamContainerLogs("cf-pigbot-link-1");

		await prepareDataBeforeAll();
	},
	// Long await time because it takes forever to download images from our registry
	30 * 60 * 1000,
);

let workerAndConnection: { worker: Worker; connection: NativeConnection };

beforeEach(async () => {
	if (workerAndConnection === undefined) {
		// Start the worker
		const { createWorker } = await import("../src/runWorker");
		workerAndConnection = await createWorker();
		// eslint-disable-next-line no-console
		workerAndConnection.worker.run().catch(console.error);
	}
}, 60_000);

afterAll(async () => {
	workerAndConnection.worker.shutdown();

	while (workerAndConnection.worker.getState() !== "STOPPED") {
		await new Promise((resolve) => setTimeout(resolve, 1_000));
	}

	await workerAndConnection.connection.close();

	await environment.down();
}, 60_000);

function pigManTestDb() {
	const container = environment.getContainer("postgres-1");

	return postgres({
		host: container.getHost(),
		port: container.getMappedPort(5432),
		user: "cloudfarms",
		password: "cloudfarms",
		database: "x-25-version",
	});
}

async function prepareDataBeforeAll() {
	const sql = pigManTestDb();

	await sql.begin(async (sql) => {
		await sql`select set_system_persona(${TestData.testUserContext.farmId})`;
		await sql`INSERT INTO farm_timeline (since, until, description)
							VALUES ('2012-02-07 15:18:32.415000 +00:00', '2012-04-07 15:18:32.415000 +00:00', 'Event within year from from'),
										 ('2010-02-07 15:18:32.415000 +00:00', NULL, 'Event starting outside the range, but still pending'),
										 ('2013-02-07 15:18:32.415000 +00:00', '2013-04-07 15:18:32.415000 +00:00', 'Event inside periods'),
										 ('2010-02-07 15:18:32.415000 +00:00', '2010-04-07 15:18:32.415000 +00:00', 'Event outside');
		`;
	});
}

test(
	"run getRelevantData",
	async () => {
		const { getRelevantData, relevantDataToText } = await import("pigbot-core/src/relevant-data/RelevantDataService");

		const response = await getRelevantData({
			farmHoldingId: TestData.testUserContext,
			langCode: "en",
			periods: [
				{ to: "2013-12-31", from: "2013-01-01" },
				{ to: "2012-12-31", from: "2012-01-01" },
			],
			indicatingKPIs: ["ALL_FINAL_AMOUNT", "SOW_FINAL_AMOUNT", "LITTER_SOW_YEAR"],
			reportKpis: ["LATE_ABORTIONS", "ALIMENTARY_WEEK"],
			breeds: null,
		});

		const text = relevantDataToText(response.relevantData, { periodNames: { 0: "Period 1", 1: "Period 2" }, periodsMatchMonths: false });

		expect(text).toMatchSnapshot();
	},
	DefaultTestLength,
);

test("run GetFarmInfo", async () => {
	const { getFarmInfo } = await import("pigbot-core/src/eff-report/EfficiencyReportService");
	const response = await getFarmInfo(
		TestData.testUserContext,
		[
			{ to: "2013-12-31", from: "2013-01-01" },
			{ to: "2012-12-31", from: "2012-01-01" },
		],
		"en",
	);

	expect(response).toMatchSnapshot();
}, 120_000);
