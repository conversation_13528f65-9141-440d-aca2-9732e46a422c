version: "3.8"
services:
  temporal-pg:
    environment:
      POSTGRES_PASSWORD: temporal
      POSTGRES_USER: temporal
    image: postgres:12
  temporal:
    depends_on:
      - temporal-pg
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=temporal-pg
      - JWT_SECRET=3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R
    image: registry.cloudfarms.online/temporal/auto-setup:sha-47d3921
    ports:
      - "7233"
  temporal-ui:
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    image: temporalio/ui:2.29.2
    ports:
      - "8080"
  cf-pigbot-link:
    image: registry.cloudfarms.online/cf-pigbot-link:${LINK_TAG}
    depends_on:
      - temporal
      - postgres
    volumes:
      - ./application.conf:/config/application.conf
    # For catching logs in tests
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
  # If you have done any CF DB changes in link, this image must be updated, or LinkIntegration.test.ts will fail.
  # Go to github, cloudfarms project, actions.
  # Find "ci" run on your branch (or any branch that has your DB changes), click on it.
  # Click on "detect-test-db-changes" job, then "Check if test DB image exists" step.
  # Copy the docker image name.
  postgres:
    image: registry.cloudfarms.online/test-db:7a9088cc1629b9333b2cd280c26c42bedb77857e.778da81ac8b796fcd7b5ecaf77db2d529a1649f3
    ports:
      - "5432"
