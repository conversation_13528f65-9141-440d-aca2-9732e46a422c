include required("environments/shared.conf")

db.default.url = "******************************************************************************************************************"
db.auth.url = "**********************************************************************************************************************"
db.reports.url = "***************************************************************************************************************************************"

dd.env=test

pigbot {
    temporal {
        address = "temporal:7233"
        https = false
        jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2Vfcm9sZXMiOnsiZGVmYXVsdCI6ImFkbWluIn19.XxHHJGZd-6RwR_k1zXwVmrIPbxGvbLT_kEbpuv42Gf4"
        namespace = "default"
    }
}