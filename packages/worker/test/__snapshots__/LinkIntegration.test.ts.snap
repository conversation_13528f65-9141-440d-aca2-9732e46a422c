// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`run GetFarmInfo 1`] = `
{
  "countryCode": "SK",
  "farmName": "Farm 1",
  "farmTimeline": [
    {
      "description": "Event starting outside the range, but still pending",
      "from": "2010-02-07",
      "to": null,
    },
    {
      "description": "Event within year from from",
      "from": "2012-02-07",
      "to": "2012-04-07",
    },
    {
      "description": "Event inside periods",
      "from": "2013-02-07",
      "to": "2013-04-07",
    },
  ],
  "kpiGoals": [],
  "settings": [
    {
      "code": "default_par1_weight",
      "description": "The default weight for first farrowed sows",
      "label": "Default weight of first farrowed sow",
      "unit": null,
      "value": "170.0",
    },
    {
      "code": "include_batches_in_feed_cons",
      "description": "Include batches in feed consumption KPIs per location [boolean]",
      "label": "Include batches in feed consumption KPIs per location",
      "unit": null,
      "value": "false",
    },
    {
      "code": "include_mummies_in_total_born",
      "description": "If this setting is set to "false", then mummified will not be a part of the calculation of total born as the calculation will then be "liveborn + stillborn". If the setting is set to "true", then the calculation instead will be "liveborn + stillborn + mummified".",
      "label": "Include mummified in calculation of total born",
      "unit": null,
      "value": "false",
    },
    {
      "code": "lactation_days_avg",
      "description": "Average days for lactation",
      "label": "Average days for lactation",
      "unit": null,
      "value": "28",
    },
    {
      "code": "litter_sow_year_algorithm",
      "description": "Algorithm for calculating litter per sow per year. 1 = NPD based, 2 = Farrowing based",
      "label": "Algorithm for calculating litter per sow per year",
      "unit": null,
      "value": "1",
    },
    {
      "code": "newborn_weight",
      "description": "Newborn piglet weight, default 1 kg",
      "label": "Newborn piglet weight",
      "unit": "kg",
      "value": "1.0",
    },
    {
      "code": "sow_production_with_nursery",
      "description": "Sow production with nursery [boolean]",
      "label": "Sow production with nursery",
      "unit": null,
      "value": "false",
    },
  ],
}
`;

exports[`run getRelevantData 1`] = `
{
  "additionalKpis": "KPI: Pre-wean mortality including fosterings by weaned sows [%]
KPI description: Ratio of dead piglets (without death born) to number of live born piglets including fostered-in piglets excluding fostered-out piglets. All values (dead piglets, live born piglets and fostered in and out piglets) are based on sows that weaned in selected period. That means the entries for dead and fostered piglets might be outside of calculated period and could be counted multiple times, each for every period in which was weaned. KPI is expressed as a percentage.
Period 1: 0.1
Period 2: 0

KPI: Weaned piglets [№]
KPI description: Provides total number of weaned piglets. Optionally the result is to be filtered according to sow breeds filter value.
Reporting interval is applied to weaning date. In case of service groups the reporting interval is applied to related servings.

Period 1: 1913
Period 2: 4943

KPI: % sows bred within 7 days
KPI description: This KPI reflects instances where sows are successfully bred within seven days following their last weaning. It provides a measure of efficiency in breeding, capturing the cases where the interval between weaning and breeding is minimized to optimize reproductive cycles.
Period 1: 10.3
Period 2: 62.2

KPI: Non productive days [№]
KPI description: Calculates the total non-productive days for sows, accumulated through various events-based interruptions in a sow's reproductive cycle within the reporting timeframe. These interruptions could stem from failed servings, weaning intervals, sales, deaths, or abortions, tracking days when sows are not contributing to production.
Period 1: 10142
Period 2: 48435

KPI: Produced piglets [№]
KPI description: Calculates the number of piglets produced based on sow production settings. The total is determined using either nursery production calculations or suckling pig production models, depending on settings. Factors involved include animal weights at key stages and intervals during the process.
Period 1: 3887
Period 2: 4907

KPI: Returns [№]
KPI description: This KPI calculates the total number of failed cycles for sows that are not successfully pregnant, collating various return scenarios including early, regular, irregular, and late returns. It encompasses data from both re-servicing and pregnancy scans to provide a comprehensive measure of cycle failures.
Period 1: 4
Period 2: 11

KPI: Dead sows and gilts [№]
KPI description: Calculate the total number of deceased sows over a given reporting period, solely focusing on those beyond the insemination phase.
Period 1: 36
Period 2: 60

KPI: Ins. gilts and sows dead amount [№]
KPI description: This metric calculates the total number of deceased animals which includes both dead sows and dead inseminated gilts, during a given reporting interval.
Period 1: 36
Period 2: 60

KPI: Regular returns (18-24 days) [№]
KPI description: Tracks the frequency of regular returns, highlighting sows that did not progress to pregnancy, where the interval between initial service and return to estrus is typically 18 to 24 days.
Period 1: 0
Period 2: 1

KPI: Farrowings / week [№]
KPI description: Provides the average number of sow farrowings occurring weekly within the specified report interval. This metric helps in understanding the frequency of sow deliveries over a given time frame. It typically considers valid farrowings based on predefined parameters set for the farm operations.
Period 1: 6
Period 2: 6

KPI: Pre-wean mortality [%]
KPI description: This metric determines the preweaning mortality rate by comparing the total number of live born piglets to the total that survive until weaning. It accounts for preweaning losses and provides insight into early-life survival rates within the given reporting period. Sow breeds can be filtered to focus the analysis further.
Period 1: 15.1
Period 2: -31.4

KPI: Weaning to fertile insemination avg. days
KPI description: Calculated as days from a weaning and until the sow is served with an insemination that leads to a farrowing and divided by number of this weanings.
Period 1: 136.03
Period 2: 169.93

KPI: Irregular returns (25-37 days) [№]
KPI description: This KPI calculates the instances where sows, expected to be pregnant, demonstrate irregular returns to estrus. It focuses on those returns taking place between 25 and 37 days after the previous serving, highlighting potential issues in the sow's reproductive cycle.
Period 1: 1
Period 2: 0

KPI: Dead to entry pigs in location [%]
KPI description: This KPI represents the percentage of animals that have died relative to the number that have entered. The calculation considers all animal deaths in relation to the total number of animals bought, transferred from other locations, or relocated in batches during the reporting interval.
Period 1: 0.03
Period 2: 0

KPI: Farrowing rate [%]
KPI description: Farrowing rate measures the success of inseminations resulting in farrowings. It calculates the proportion of successful farrowings to the total number of servings within a specific time frame, adjusted by the farm's settings for gestation period. This KPI gives insights into the reproductive efficiency and potential areas for management improvements.
Period 1: 73.6
Period 2: 78.5

KPI: Served after 7 days per week [№]
KPI description: This KPI calculates the weekly average of instances where the time interval between a sow's last weaning in the previous cycle and its first serving in the current cycle exceeds seven days. It provides a weekly perspective on service efficiency in the sow's reproductive cycle.
Period 1: 1
Period 2: 1

KPI: Farrowing interval avg. [Days]
KPI description: Number of consecutive farrowings pairs in period. Parity 2 sows and higher that farrowed in the period --> how many farrowings and previously farrowings pair.
Period 1: 226
Period 2: 162.1

KPI: Dead sows [№]
KPI description: Provide amount of dead sows in reporting interval. Sows with parity 0 (inseminated gilt) are not included.
Period 1: 36
Period 2: 49

KPI: Dead sows ratio [%]
KPI description: This metric calculates the percentage of total sows that have either died or been sold within a given interval. It effectively measures the proportion of sow exits due to deaths in comparison to sales out of the total exits.
Period 1: 100
Period 2: 100

KPI: Pregnant in week 8 [%]
KPI description: This KPI calculates the percentage of successful pregnancies by comparing the number of sows that are pregnant by the 8th week to the total number of inseminations during that interval, excluding sold pregnant sows, providing insight into the effectiveness of breeding strategies.
Period 1: 91.4
Period 2: 94

KPI: Successful services in period [№]
KPI description: Number of served sows in period which is still pregnant from that service and where the service didn't failed (ended with a repeat service, dead, abortion, neg. Pregn. check or selling)
Period 1: 138
Period 2: 273

KPI: Stillborns [%]
KPI description: Represents the percentage of piglets that are born dead out of the total number of piglets born during the report interval. This calculation reflects the incidence of non-viable births and is derived by dividing the number of dead born piglets by the overall count of piglets, including potentially mummified ones if included.
Period 1: 3.4
Period 2: 3.6

KPI: Piglet mortality including still born [%]
KPI description: The percent of stillborn piglets and registered dead piglets out of the amount of liveborn piglets in the period.
Period 1: 3.52
Period 2: 3.65

KPI: Vital liveborn piglets [%]
KPI description: This KPI measures the proportion of liveborn piglets relative to a specified benchmark or indicator within the farm. By offering insights into breeding efficiency, it aids in identifying areas in need of improvement for farmers. It can be aligned with other production reports for optimal insight.
Period 1: 100
Period 2: 100

KPI: Females dead/year [%]
KPI description: This KPI reflects the annual mortality rate of breeding females relative to their average population size over the reporting period. It considers the proportion of deceased sows and gilts compared to the average number of females over time, then scales this figure to a yearly basis, accounting for varying interval lengths, and expresses it as a percentage.
Period 1: 0.24
Period 2: -262.17

KPI: Pregnant in week 7 [%]
KPI description: Calculates the percentage of successful pregnancies, measuring how many sows remain pregnant up to the 7th week following insemination. This ratio is derived from comparing successful pregnancies to the total number of initial servings over the aligned interval. Sold pregnant sows can be optionally excluded from the calculations, depending on the settings.
Period 1: 91.7
Period 2: 94.6

KPI: Feeding days per produced pig [days]
KPI description: Provides the average number of feeding days per pig produced by dividing the total feeding days by the total number of pigs produced within the reporting interval.
Period 1: 51
Period 2: 

KPI: Weaned piglets / week [№]
KPI description: Determines the average number of piglets weaned per week over a specified reporting interval. This KPI helps in evaluating the weekly weaning efficiency and can be filtered based on selected sow breeds to assess specific performance within those groups.
Period 1: 37
Period 2: 95

KPI: Dead piglets before weaning [%]
KPI description: Represents the proportion of piglets that do not survive to weaning as a fraction of total piglets born in a given period. This KPI offers insight into the mortality rate among newborn piglets, enabling farmers to assess and improve the health and management conditions affecting early-stage survival.
Period 1: 0.13
Period 2: 0

KPI: First service to last service interval [days]
KPI description: Measures the average duration, in days, from the first serving to the last serving in a sow's reproductive cycle, aligned with the reporting timeframe.
Period 1: 45.5
Period 2: 39.7

KPI: Live borns / sow / year [№]
KPI description: Provides the annual total of live-born piglets per sow, calculated by multiplying the average litter size by the annual litter number for each sow.
Period 1: 13.8
Period 2: 5.7

KPI: Matings per service
KPI description: This indicator reflects the average number of matings per sow required to achieve successful service. It calculates the efficiency of matings by dividing the total matings by the total services conducted within the reporting period.
Period 1: 1.9
Period 2: 1.9

KPI: Replacement rate at weaning [%]
KPI description: The weaning-based replacement rate is expressed as a percentage and indicates the rate at which sows are replaced in relation to their production cycles. It is calculated by multiplying the litter rate per sow per year by the number of successful first weanings for parity one sows, then dividing by the total successful weanings during the reporting interval.
Period 1: 0
Period 2: 25.8

KPI: Avg. number of inseminated gilts [№]
KPI description: The average amount represents the mean quantity or value of a specified criterion within a defined reporting interval. It involves calculating the total of that criterion divided by the number of time periods or countable instances within the interval, offering an overall summary or assessment of performance.
Period 1: 79
Period 2: 163

KPI: Total failed cycles [№]
KPI description: Provides a comprehensive count of sows that have experienced failed cycles. This includes those returning to heat after breeding, those scanning negative for pregnancy, those that died during a failed cycle, those sold for slaughter without a specified reason for removal, those undergoing abortion, and those open past a standard interval.
Period 1: 52
Period 2: 73

KPI: Servings in farrowing rate [№]
KPI description: Provides the number of servings where farrowings are expected in requested report interval.
Expectation is done via utilizing \`farrowingRateShiftDays\` parameter which shifts reporting interval by parameter-defined number of days
to past - to extract servings from such adjusted interval. And thanks to it user defined interval describes time of expected
farrowings and not time of servings. This number is used to calculate farrowing rate.

Period 1: 276
Period 2: 270

KPI: Weaned piglets per weaning [№]
KPI description: Calculates the average number of piglets weaned per weaning. This KPI offers insights into the efficiency of the weaning process by providing the ratio of total weaned piglets to the total number of weanings. Optional filters may apply based on sow breed, ensuring the metric aligns with specific breeding strategies.
Period 1: 11.3
Period 2: 15.6

KPI: Stillborn [№]
KPI description: Provides the number of dead born piglets from farrowings of sows in the report interval.
Data are taken from either farrowings that match report interval or servings that match report interval
based on the \`useServiceGroups\` parameter.

Period 1: 166
Period 2: 175

KPI: Weanings per week [№]
KPI description: Represents the average number of weanings completed on a weekly basis during the reporting period. This measure considers weanings filtered by specific sow breeds if applicable, reflecting their frequency within the designated timeframe.
Period 1: 3
Period 2: 6

KPI: Pregnancy in week 6 [%]
KPI description: This KPI calculates the pregnancy success rate after 6 weeks from insemination by comparing the number of sows that remain pregnant to the total number of servings within the report interval. It's presented as a percentage, indicating the effectiveness of the insemination process while excluding sows sold as pregnant, if specified.
Period 1: 91.9
Period 2: 95.1

KPI: Repeat interval [days]
KPI description: Days from service to reservice and reservice to reservice. Total number of days from service to reservice+reservice to reservice for sows and gilts with a reservice in the period.
Period 1: 454.96
Period 2: 6990.79

KPI: Pregnancy at 6 weeks [№]
KPI description: Provides the number of pregnant sows that reached 6-th week of pregnancy without failure (negative scan, dead, re-serving or slaughter sell) from insemination. It is used for calculation of pregnancy ratio. Data are taken from sow servings that match the report interval shifted by 6 weeks to the past. Setting \`excludeSoldPregnantSows\` determines if sold pregnant sows are excluded from this number.
Period 1: 182
Period 2: 311

KPI: Litter reconciliation
KPI description: This KPI calculates the number of piglets that are unaccounted for, by subtracting the sum of weaned piglets and dead piglets from the number of liveborn piglets within the reporting interval.
Period 1: 2804
Period 2: -323

KPI: Served after 7 days [№]
KPI description: Provides total number of cases where number of days between last weaning in preceding cycle
and first sow serving in analyzed cycle of sows
is greater than 7 days.
First servings of analyzed cycles are from reporting interval.
Period 1: 61
Period 2: 45

KPI: Farrowings [№]
KPI description: Provides the number of farrowings of sows in report interval.
Late abortions are not included if setting \`treatZeroLiveBornAsAbortion\` is set and the number of liveborn is zero.
Data are taken from either farrowings that match report interval or servings that match report interval
based on the \`useServiceGroups\` parameter.

Period 1: 295
Period 2: 324

KPI: Non productive days based litter / sow / year [№]
KPI description: This metric evaluates the number of litters produced per sow annually, considering non-productive days in the calculation. It adjusts for the fraction of the year that sows are non-productive and uses either real feeding days or total feeding days in conjunction with gestating and lactation days, depending on whether specific breeds are filtered. The goal is to provide insight into a sow's yearly litter output accounting for time lost due to non-productive periods.
Period 1: 0.86
Period 2: 0.4

KPI: Conception rate
KPI description: Ratio reflecting the efficiency of successful pregnancies out of total servicer attempts. A higher value indicates greater reproductive success and less need for repeat services within the defined time frame.
Period 1: 85.7
Period 2: 79.8

KPI: Farrowing rate of first farrowing sows [%]
KPI description: This metric measures the farrowing success of first parity sows by comparing the number of successful farrowings to the number of servings attempted within the specified reporting period, adjusted for the expected gestation period. The calculation considers only those servings not excluded by farm-specific criteria, such as sales or purchases of pregnant sows.
Period 1: 97.9
Period 2: 88.2

KPI: Farrowing index
KPI description: Measures the efficiency of a breeding program by evaluating the number of farrowings a sow can produce annually. Specifically, it averages the number of litters per sow within a year by taking the reciprocal of the average interval between farrowings, scaled to a 365-day year. This metric helps in assessing the reproductive performance and management effectiveness in pig production.
Period 1: 1.62
Period 2: 2.25

KPI: Pregnant in week 4 [№]
KPI description: Provides the number of pregnant sows that reached 4-th week of pregnancy without failure (negative scan, dead, re-serving or slaughter sell) from insemination. It is used for calculation of pregnancy ratio. Data are taken from sow servings that match the report interval shifted by 4 weeks to the past. Setting \`excludeSoldPregnantSows\` determines if sold pregnant sows are excluded from this number.
Period 1: 173
Period 2: 324

KPI: Weaned piglets / sow / year [№]
KPI description: This KPI calculates the annual number of weaned piglets per sow. It is determined by multiplying the average number of weaned piglets per litter by the number of litters per sow each year.
Period 1: 13.4
Period 2: 7.3

KPI: Sows served [№]
KPI description: Provides number of all first servings in sows' cycles.
Servings are from reporting interval.

Period 1: 107
Period 2: 233

KPI: Early returns (< 18 days) [№]
KPI description: This metric tracks the occurrences of sows unexpectedly returning to estrus shortly after being assumed pregnant, where the interval from the initial insemination to the early return does not exceed 17 days. This indicator helps in monitoring sow fertility challenges within a defined reporting timeframe.
Period 1: 1
Period 2: 5

KPI: Pregnant in week 3 [%]
KPI description: Calculates the percentage of sows that successfully reached the third week of pregnancy without incidents, compared to all servings. This metric provides an essential insight into the effectiveness of the sow's insemination practices, adjusting the data by a three-week interval to account for the gestation timeline. The metric excludes sows sold during pregnancy, allowing for a clearer analysis of pregnancy success rates.
Period 1: 94
Period 2: 97

KPI: Pregnant in week 12 [%]
KPI description: This KPI calculates the percentage of sows that successfully reach the 12th week of pregnancy without any issues such as negative scan results, death, re-serving, or being sold, out of the total number of servings. This calculation excludes sows sold as pregnant and is based on data taken from sow servings within the designated report interval shifted 12 weeks prior.
Period 1: 90
Period 2: 92.2

KPI: Services [№]
KPI description: Provides number of all servings (first servings + re-servings).
Servings are from reporting interval.

Period 1: 161
Period 2: 342

KPI: Sows remove rate [%/year]
KPI description: This metric quantifies the percentage rate at which breeding sows are either sold or deceased within a specific timeframe, annually adjusted. The calculation takes into account all sold and deceased sows compared to the average number of sows present over the duration, offering insights into herd turnover efficiency.
Period 1: 14.2
Period 2: 22.6

KPI: Gilts local decrease [№]
KPI description: The total reduction in the number of gilts within a specific location, accounting for those that were transferred out, had their first servings, or were reclassified during the reporting interval.
Period 1: 17
Period 2: 72

KPI: Breeding piglets per litter [№]
KPI description: Indicates the average number of female piglets born per litter during the report interval. This data is derived from farrowing records that align with the set period and focuses on births specifically attributed to the sows' breed.
Period 1: 0.6
Period 2: 0.3

KPI: Pregnant in week 5 [%]
KPI description: Represents the proportion of sows that successfully remain pregnant by the fifth week after insemination. To calculate this, the number of successfully pregnant sows is divided by all servings within the report interval and then multiplied by 100. This KPI helps evaluate the effectiveness of the breeding process.
Period 1: 92.6
Period 2: 95.8

KPI: Avg. weight at entry [kg]
KPI description: Calculates the average weight of young breeding animals at entry. This measure aggregates the total weight of bought, reclassified, and transferred young breeding animals, dividing it by the total number of these animals to determine the average weight for the reporting interval.
Period 1: 13.1
Period 2: 0

KPI: Mummified born ratio [%]
KPI description: The percentage of mummified piglets out of the total number born within the report interval. This ratio uses the count of mummified piglets from farrowings of sows and divides it by the total number of piglets born, including those mummified if set to be included. The result is then multiplied by 100 to express as a percentage.
Period 1: 0.1
Period 2: 0.3

KPI: Dead sows, gilts and YBA [№]
KPI description: Summarizes the total number of deceased sows and young breeding animals (gilts) during the reporting interval. It combines the count of dead sows, including inseminated gilts, with the total amount of dead young breeding animals.
Period 1: 36
Period 2: 60

KPI: Piglet avg. weight at period end [kg]
KPI description: This metric calculates the average weight of piglets at the end of a reporting interval. The value is determined by taking the most recent average weight recorded at the last stocktaking or by using a formula that adjusts the weaning weight, factors newborn weight, and adjusts by a specific coefficient to provide a practical estimate.
Period 1: 3.1
Period 2: 3.1

KPI: Days from weaning to 1st service
KPI description: This KPI calculates the average number of days from the last weaning in the previous cycle to the first sow serving in the current analyzed cycle. It focuses on first servings during the specified reporting interval.
Period 1: 104.7
Period 2: 14.5

KPI: >>> NPD (service to exit)
KPI description: Calculates the non-productive days between a failed serving and a subsequent sow exit, normalized by the litter production. It reflects delays in the reproduction cycle potentially due to death or sale after failed service attempts, providing insights into efficiency and downtime within a sow's production cycle.
Period 1: 0
Period 2: 59.7

KPI: Dead piglets before weaning [№]
KPI description: Provides total number of dead piglets (If \`indGiltDeathIgnored\` setting is set to false then individual gilts located in sow or piglet locations are included). If farm parameter for use of serving groups is active, then serving from which dead piglets origin has to be in reporting interval. Otherwise death has to be in reporting interval.
Period 1: 6
Period 2: 0

KPI: Age at first service [days]
KPI description: This indicator represents the age in days when gilts are first served, based on available data within the specified reporting period. It calculates the average initial serving age by considering the age of each gilt at the time of her first service.
Period 1: 278
Period 2: 180

KPI: % gilts of all first services
KPI description: This KPI tallies the total number of initial service events involving gilts during the reporting period. It focuses on maiden services for young female pigs.
Period 1: 13.7
Period 2: 23.6

KPI: Still and mummificated born [№]
KPI description: Calculates the combined number of dead born and mummified piglets from farrowings of sows within the specified report interval. The data is compiled by considering available farrowings or servings that align with the report interval, contingent upon the setting of the \`useServiceGroups\` parameter.
Period 1: 169
Period 2: 190

KPI: Weaning weight per litter [kg]
KPI description: Measures the average weight of piglets at the time of weaning for each litter. This indicator reflects the growth and health of newborn piglets when they are separated from the sow. Optionally filtered based on specific breed criteria.
Period 1: 96.1
Period 2: 114.3

KPI: Inseminated gilts [%]
KPI description: Calculates the ratio of inseminated gilts to sows at the end of the reporting period. This metric helps assess the balance within the herd, indicating how many of the population are in the early stages of reproduction compared to the established sows.
Period 1: 20.8
Period 2: 42.9

KPI: Dead pigs [№]
KPI description: Determine the quantity of deceased animals occurring within a defined period, assisting in evaluating animal health and management efficiency during that timeframe.
Period 1: 6
Period 2: 0

KPI: Gilts served [№]
KPI description: Provides number of all first (maiden) servings of gilts.
Servings are from reporting interval.

Period 1: 17
Period 2: 72

KPI: Avg. repeat interval
KPI description: Calculates the average interval between repeated servings. It is determined by dividing the total repeated serving days by the number of repeat servings during the reporting interval.
Period 1: 12
Period 2: 189

KPI: Litter / sow / year [№]
KPI description: This key performance indicator estimates the annual litter production per sow. It accounts for various calculation methods, including utilizing non-productive days, farrowing data or the sow's reproductive cycle, depending on configured algorithms. The approach dynamically adapts based on settings, to reflect realistic sow productivity within the assessment timeframe.
Period 1: 0.86
Period 2: 0.4

KPI: Parity 1 total born / litter [№]
KPI description: Count of live borns, dead borns and mummified borns per farrowings for parity 1 sows.
Period 1: 15.91
Period 2: 14.59

KPI: Parity 1 total born [№]
KPI description: Count of live borns, dead borns and mummified borns for parity 1 sows.
Period 1: 1702
Period 2: 2437

KPI: Pregnant in week 3 [№]
KPI description: Provides the number of pregnant sows that reached 3-th week of pregnancy without failure (negative scan, dead, re-serving or slaughter sell) from insemination. It is used for calculation of pregnancy ratio. Data are taken from sow servings that match the report interval shifted by 3 weeks to the past. Setting \`excludeSoldPregnantSows\` determines if sold pregnant sows are excluded from this number.
Period 1: 172
Period 2: 324

KPI: Liveborn ratio [%]
KPI description: This KPI represents the percentage of piglets born alive compared to the total number of piglets born, including dead and mummified births, depending on your farm's settings.
Period 1: 96.6
Period 2: 96.4

KPI: Young breeding animals feeding days
KPI description: Provides total number of animal-days for young breeding animals when they were alive and fed within reporting interval.
Period 1: 5275036
Period 2: -105269

KPI: NPD / sow / year (based on avg. sows)
KPI description: Calculates the annualized non-productive days per sow for the US market. This is derived by dividing the total non-productive days accrued by sows, adjusted for the average number of sows over the reporting interval. The formula accounts for the standard year length, normalizing the figure across 365.25 days.
Period 1: 40.16
Period 2: 182.55

KPI: Stillborn per litter [№]
KPI description: Calculates the average number of dead born piglets per litter for sows in the given reporting period. This metric helps in assessing the health and management of breeding practices by providing insights into the number of piglets that did not survive birth across all litterings reported within the specified interval.
Period 1: 0.6
Period 2: 0.5

KPI: Empty sows (>60 days, no reserved) [%]
KPI description: This KPI indicates the percentage of pregnant sows that fail to be confirmed by a pregnancy scan, with more than 60 days having elapsed since the previous serving. The calculation takes into account either serving data or sow scan data, within a specific reporting interval, contingent upon the configuration of the service group parameter. The purpose of this metric is to evaluate the effectiveness and efficiency of the breeding cycle by highlighting occurrences where pregnancy did not progress as planned.
Period 1: 3.11
Period 2: 0

KPI: 30 days fertility for served in period [%]
KPI description: Indicates the fertility rate of sows by calculating the percentage of successful pregnancies that have progressed to 30 days within the selected interval. The percentage is derived by dividing the number of 30-day pregnancies by the total number of servings in the interval and multiplying by 100.
Period 1: 87.6
Period 2: 96.8

KPI: Pregnant in week 10 [№]
KPI description: Provides the number of pregnant sows that reached 10-th week of pregnancy without failure (negative scan, dead, re-serving or slaughter sell) from insemination. It is used for calculation of pregnancy ratio. Data are taken from sow servings that match the report interval shifted by 10 weeks to the past. Setting \`excludeSoldPregnantSows\` determines if sold pregnant sows are excluded from this number.
Period 1: 197
Period 2: 305

KPI: Average parity number [№]
KPI description: Determines the average number of parities for active sows in the reporting interval. It helps in assessing the reproductive frequency of sows that have been inseminated before the period ends and remain in the herd or have not been sold for slaughter.
Period 1: 2.4
Period 2: 1.5

KPI: Negative pregnancy check [№]
KPI description: Number of sows and gilts with a negative pregnancy check in the period, but where the code does not have a mark for Abortion or Heat.
Period 1: 5
Period 2: 0

KPI: Parity 1 stillborns/litter [№]
KPI description: Measures the average number of dead born piglets per litter from sows that attain first parity within the specified reporting period. This metric gives insights into the reproductive outcomes of first parity sows, capturing data from either farrowings or servings corresponding to the report interval, contingent on the parameter set.
Period 1: 0.5
Period 2: 0.5

KPI: Young breeding animals local decrease total weight [kg]
KPI description: Provides the combined total weight of gilts that have been either transferred out, served, or reclassified within the local facility during the reporting interval. This measurement reflects changes in the local stock due to internal operations and transfers.
Period 1: 2040
Period 2: 8640

KPI: Sows mortality rate [%/year]
KPI description: The proportion of sows, including inseminated gilts, that died within the reporting interval, standardized over the entire year and expressed as a percentage of the average sow population during that interval.
Period 1: 14.2
Period 2: 22.6

KPI: Lactation period [days]
KPI description: Calculates the average number of days a sow spends in lactation, from farrowing to regular weaning of her piglets. This measure can be filtered by specific breeds when applicable.
Period 1: 29
Period 2: 30

KPI: Weaned piglets per litter [№]
KPI description: Calculates the average number of piglets weaned per litter during a reporting interval. This metric helps in assessing the efficiency of weaning processes by indicating how many piglets, on average, are carried through to weaning from each litter. The calculation considers piglets' weaning without nursery adjustments and can be influenced by the breed filter.
Period 1: 15.6
Period 2: 18.4

KPI: Dead sows, gilts and YBA pr week [№]
KPI description: Calculate the average weekly number of deceased sows and gilts by multiplying the total deaths of these animals by seven and dividing by the number of days within the reporting interval.
Period 1: 1
Period 2: 1

KPI: Pregnant in week 10 [%]
KPI description: The pregnancy success rate, calculated as the percentage of sows that successfully advance to the tenth week of pregnancy among all servings within a given time frame. This metric takes into account sows that are inseminated during the evaluation interval ten weeks prior, with an optional setting to exclude those sold while pregnant.
Period 1: 90.8
Period 2: 93

KPI: 1st weaning [%]
KPI description: Represents the proportion of first-time farrowing sows that successfully completed the weaning process, excluding nursery weanings. This can be optionally filtered based on sow breeds. The reporting interval is linked to the weaning date or related servings if service groups are involved.
Period 1: 0
Period 2: 65.1

KPI: Pregnant in week 3 per week [№]
KPI description: This KPI calculates the average number of pregnant sows in their third week of pregnancy per week within a reporting interval. This value is obtained by multiplying the standard count of such sows by 7 and dividing by the number of days in the reporting interval, offering insight into weekly pregnancy progression over a given timeframe.
Period 1: 3
Period 2: 6

KPI: Piglets for breeding [№]
KPI description: Provides the number of female born piglets from farrowings of sows in the report interval.
Data are taken from either farrowings that match report interval or servings that match report interval
based on the \`useServiceGroups\` parameter.

Period 1: 175
Period 2: 111

KPI: Services / week [№]
KPI description: Calculates the average number of servings distributed each week within the report interval. This KPI considers both initial servings and repeated servings through the entire reporting period and is designed to provide a comprehensive weekly overview of serving trends and frequency.
Period 1: 3
Period 2: 7

KPI: Young breeding animals total weight gain [kg]
KPI description: This KPI calculates the total weight change of young breeding animals over the reporting interval. It considers the initial and final stock figures and weights, adds the weight of animals sold, killed, reclassified, and considers transfer movements, while adjusting for bought animals and reclassifications during the same interval. This weight gain provides an overview of weight changes through various operations such as selling, reclassification, and transfers, indicating the efficiency of breeding management within the given timeframe.
Period 1: -260493
Period 2: 14640

KPI: Empty sows (>60 days) [№]
KPI description: Measures the number of open sows that were expected to conceive but remained non-pregnant over a specified duration. The duration is marked by the number of days exceeding a set limit (such as 60 days) between the initial attempt and subsequent evaluation or rescan.
Period 1: 36
Period 2: 26

KPI: Live born [№]
KPI description: Provides the number of liveborn piglets from farrowings of sows in the report interval.
Data are taken from either farrowings that match report interval or servings that match report interval
based on the \`useServiceGroups\` parameter.

Period 1: 4723
Period 2: 4620

KPI: Sold pigs avg. weight [kg]
KPI description: Calculate the average weight of sold animals by dividing the total weight of all sold animals by the total number of animals sold.
Period 1: 21
Period 2: 

KPI: Weaned sows liveborn / litter [№]
KPI description: Indicates the average number of live-born piglets per litter for sows that have been weaned later in their lifecycle, excluding nursery weanings. This metric offers insights into reproductive performance by assessing litter size at the weaning stage, factoring in the applied breed filter where applicable. The calculation aligns with the specified reporting interval tied to the weaning event, and is reflective of broader reproductive trends.
Period 1: 16
Period 2: 13.9

KPI: Empty days before exit / sow [№]
KPI description: This KPI indicates the average non-productive days each sow experiences before being transitioned out of the production cycle. The calculation accounts for the interval beyond the last productive phase in the sow's lifecycle. The data used in this metric is determined by specific parameters, particularly focused on whether the sow's transition occurs through sale or death.
Period 1: 19
Period 2: 150.4

KPI: Late returns (45-60 days) [№]
KPI description: Represents the number of incidences where sows, initially thought to be successfully inseminated, return to estrus beyond the expected timeframe, indicating a missed cycle. This measure typically captures returns between 45 and 60 days post previous insemination attempt.
Period 1: 1
Period 2: 5

KPI: Pregnant in week 9 [%]
KPI description: Calculates the percentage of successful pregnancies by comparing the number of sows that have reached the ninth week of pregnancy without failure to the total servings. This metric reports on sow servings from the interval adjusted by nine weeks in retrospect. Exclusion of sold pregnant sows is determined by specific settings.
Period 1: 91
Period 2: 93.7

",
  "medicineUsages": null,
  "pregnancyFailures": "Total failures [№]:
Parity 0: 1
Parity 1: 3
Parity 2: 35
Parity 3: 7
Parity 4: 1
All: 47

Early return to estrus [%] (11-17 days):
Parity 1: 33.33
All: 2.13

Regular return to estrus [%]:
Parity 2: 2.86
All: 2.13

Regular - Type 1 [%] (18-24 days):
All: 0

Regular - Type 2 [%] (38-44 days):
Parity 2: 2.86
All: 2.13

Irregular return to estrus [%] (25-37 days):
Parity 4: 100
All: 2.13

Late return to estrus [%] (45-60 days):
Parity 1: 33.33
Parity 2: 2.86
All: 4.26

Empty [%]:
Parity 0: 100
Parity 2: 62.86
All: 48.94

Abortions [%]:
All: 0

Deaths [%]:
Parity 1: 33.33
Parity 2: 31.43
Parity 3: 42.86
All: 31.91

Sold to slaughterhouse [%]:
All: 0

Sold pregnant [%]:
All: 0

Scanned empty [%]:
Parity 3: 57.14
All: 8.51

",
}
`;
