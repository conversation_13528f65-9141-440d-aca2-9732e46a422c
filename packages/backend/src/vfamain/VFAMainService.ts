import { EFFICIENCY_REPORT_TYPE } from "pigbot-core/src/Constants";
import { sql } from "pigbot-core/src/database";
import { EfficiencyReportRecord } from "pigbot-core/src/EfficiencyReportData";
import { UserContext } from "pigbot-core/src/UserContext";
import { Period } from "pigbot-core/src/vfamain/Periods";

export type GetLatestEfficiencyReportSummaryRequest = {
	farmLocationId: number | null;
	idx: number;
	period: Period;
};

export async function getLatestEfficiencyReport(farmId: number) {
	const data = await sql<EfficiencyReportRecord[]>`
        SELECT * FROM report_request WHERE farm_id = ${farmId} AND type = ${EFFICIENCY_REPORT_TYPE} ORDER BY created_at DESC LIMIT 1
    `;
	if (data.length === 0) {
		throw new Error("No efficiency report found");
	}
	return data[0];
}

type SourceDoc = {
	file_data: Uint8Array;
	file_name: string;
};

export async function getPdfData(documentName: string) {
	const [row] = await sql<SourceDoc[]>`
		SELECT file_data, file_name FROM source_doc WHERE file_name = ${documentName}
	`;
	if (!row) {
		throw new Error(`PDF document '${documentName}' not found`);
	}

	// Convert the Uint8Array to Base64 for easier transmission
	const base64Data = Buffer.from(row.file_data).toString("base64");

	return base64Data;
}

export type SowFarmLocation = {
	id: number;
	name: string;
};

export type SowFarmLocations = SowFarmLocation[] | null;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function getSowFarmLocations(userContext: UserContext) {
	// Test error case
	// throw new Error('Error when loading farms');

	// Test loading case
	// await new Promise((f) => setTimeout(f, 1000));

	// Test multiple farm locations
	const multipleFarmLocations = [
		{ id: 100489, name: "Farm 100489" },
		{ id: 2309, name: "Farm 2309" },
		{ id: 999999, name: "Farm 999999" },
		{ id: 78, name: "Farm 78" },
	];

	// Test no farm location / 1 farm location
	const noFarmLocations = null;
	const oneFarmLocation = [{ id: 100489, name: "Farm 100489" }];

	const farmLocations: SowFarmLocations = multipleFarmLocations;

	// When there's only 1 farm location or none, return null - calculating KPIs should be faster when providing organization instead of locations.
	return farmLocations === null || farmLocations.length == 1 ? null : farmLocations;
}

export type SowFarmLocationsAndPeriodInfo = {
	sowFarmLocations: SowFarmLocations;
	periodLength: number | null;
};
