{"name": "pigbot-backend", "version": "1.0.0", "description": "", "scripts": {"start": "node-dev src/server.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@jest/globals": "^29.7.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.16.7", "jest": "^29.7.0", "node-dev": "^8.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "dependencies": {"@slack/web-api": "^7.5.0", "@temporalio/client": "^1.11.7", "@temporalio/proto": "^1.11.7", "@trpc/server": "11.0.0-rc.700", "@types/jsonwebtoken": "^9.0.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-rate-limit": "^7.4.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "ms": "^2.1.3", "pigbot-core": "workspace:*", "pigbot-worker": "workspace:*", "rate-limit-redis": "^4.2.0", "ts-pattern": "^5.4.0", "zod": "^3.23.8"}}