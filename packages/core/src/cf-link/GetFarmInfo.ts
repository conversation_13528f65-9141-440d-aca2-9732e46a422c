import { proxyActivities } from "@temporalio/workflow";
import { FarmId } from "../FarmId";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities, FarmTimelineEntity, GetFarmInfo, KpiGoalData, Period } from "./CfLinkActivities";
import { DateTime } from "luxon";

export type FarmInfo = GetFarmInfo & {
	farmTimeline: FarmTimelineEntity[];
	kpiGoals: KpiGoalData[];
};

export async function GetFarmInfo(farmId: FarmId, usesSeges: boolean, relevantPeriods: Period[], langCode: string): Promise<FarmInfo> {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "1m",
		taskQueue: getCfLinkQueue(farmId.env),
	});

	const timelineFromDate = DateTime.fromISO(relevantPeriods[relevantPeriods.length - 1].from).minus({ years: 1 });

	const timelineFrom = timelineFromDate.toISODate()!;

	const timelineTo = relevantPeriods[0].to;

	const [farmInfo, farmTimeline, kpiGoals] = await Promise.all([
		activities.GetFarmInfo(farmId.farmId, usesSeges),
		activities.GetTimeline(timelineFrom, timelineTo, farmId.farmId, langCode),
		activities.GetKpiGoals(farmId.farmId, timelineTo, langCode),
	]);

	return {
		...farmInfo,
		farmTimeline,
		kpiGoals,
	};
}
