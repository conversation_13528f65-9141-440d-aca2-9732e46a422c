import { FarmId } from "../FarmId";
import { proxyActivities } from "@temporalio/workflow";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities, KpiGoalData } from "./CfLinkActivities";

/**
 * Workflow that retrieves KPI goals for a specific farm on a given date
 * @param farmId The farm ID
 * @param date The date to check for active goals (ISO format string)
 * @param langCode The language code for KPI descriptions
 * @returns A list of KPI goals with their details
 */
export async function GetKpiGoals(params: { farmId: FarmId; date: string; langCode: string }): Promise<KpiGoalData[]> {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "5m",
		taskQueue: getCfLinkQueue(params.farmId.env),
	});

	return await activities.GetKpiGoals(params.farmId.farmId, params.date, params.langCode);
}
