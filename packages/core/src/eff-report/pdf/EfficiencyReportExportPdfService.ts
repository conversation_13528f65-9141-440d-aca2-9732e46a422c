import fs from "fs/promises";
import * as mime from "mime-types";
import logger from "../../logger";
import { renderTemplateFile } from "../../utils/renderTemplateFile";
import path from "path";
import { marked } from "marked";
import puppeteer from "puppeteer";
import { cachePromise } from "../../Cache";
import { EffReportSummaryReqResData } from "../EfficiencyReportService";
import { internalChunk2CustomerChunk } from "../EffReportSummaryResponseChunk";
import { extractDataFromEffReportAnalysisArray } from "../ExtractDataFromEffReportAnalysisArray";
import { match } from "ts-pattern";
import { FarmInfo } from "../../cf-link/GetFarmInfo";
import { splitByHorizontalRule } from "../../utils/markdownUtils";
import { convertJsonToGraphOpt } from "../GraphConverters";
import { Renderer } from "marked";

async function fileToBase64(filePath?: string): Promise<string | undefined> {
	if (!filePath) {
		return undefined; // No file path provided
	}
	try {
		const fileBuffer = await fs.readFile(filePath); // Read file as Buffer
		const base64String = fileBuffer.toString("base64"); // Convert Buffer to base64

		const mimeType = mime.lookup(filePath) || "image/png"; // Get MIME type, default to png if not detected
		return `data:${mimeType};base64,${base64String}`; // Construct Data URI
	} catch (error) {
		logger.error(`Error converting file to base64`, error);
		return undefined; // Return undefined if there's an error
	}
}

// TODO: Format date based on format from settings. This is a workaround to make sure the format is good enough.
/**
 * Format a date string using a language code
 * @param dateStr The date string to format
 * @param languageCode The language code (e.g., 'en', 'de', 'fr')
 * @returns The formatted date string
 */
function formatDateByLanguage(dateStr: string, languageCode: string): string {
	try {
		// Try to parse the string into a Date object
		const parsedDate = new Date(Date.parse(dateStr));

		// Check if the date is valid
		if (isNaN(parsedDate.getTime())) {
			// Invalid date - log a warning and return the original string
			logger.warn(`Invalid date format encountered: "${dateStr}". Returning original string.`);
			return dateStr;
		}

		// Create a formatter using the country code with date-only options
		const formatter = new Intl.DateTimeFormat(languageCode, {
			year: "numeric",
			month: "long",
			day: "numeric",
		});

		return formatter.format(parsedDate);
	} catch (error) {
		// Log the error and return the original string
		logger.warn(`Error formatting date "${dateStr}" for language code ${languageCode}: ${error}. Returning original string.`);
		return dateStr;
	}
}

export async function getEfficiencyReportPDF({ responseId, hiddenSections }: { responseId: string; hiddenSections: Set<string> }) {
	// VFA response stream is cached and this loads it from redis
	const cache = await cachePromise;
	const reqResData = await cache.get<EffReportSummaryReqResData>(responseId);
	if (!reqResData) throw new Error(`No response found for the given ID ${responseId}`);

	// Get farm info from the stream
	let farmInfo: FarmInfo | undefined;
	reqResData.internalResponseStreamArray.forEach((c) =>
		match(c)
			.with({ type: "farmInfo" }, (v) => (farmInfo = v.farmInfo))
			.otherwise(() => {}),
	);
	if (!farmInfo) throw new Error(`No farm info found in the response`);

	// Use the same extraction method used on the frontend to parse data from the stream
	const responseData = extractDataFromEffReportAnalysisArray(
		// The stream is the internal stream that needs to be converted to normal stream
		reqResData.internalResponseStreamArray.flatMap((c) => internalChunk2CustomerChunk(c, false)),
	);

	if (!responseData.response) throw new Error("No response found in the data");
	if (!responseData.causeAnalyses) throw new Error("No causeAnalyses found in the data");
	if (!responseData.issues) throw new Error("No issues found in the data");

	const renderer = new Renderer();

	renderer.code = ({ text, lang }) => {
		if (!lang || !text) {
			return `<pre><code class="${lang ? `language-${lang}` : ""}">${text}</code></pre>`;
		}

		const { convertedOptions, explanation, height } = convertJsonToGraphOpt(lang, text);

		if (convertedOptions) {
			try {
				const convertedOptionsWithoutAnimations = {
					...convertedOptions,
					animation: false,
				};

				return `
					<div style="page-break-inside: avoid; margin-bottom: 30px; margin-top: 30px;">
						<div class="chart-container" style="height:${height}px;">
							<script>
								(function() {
									const container = document.currentScript.parentElement;
									const chart = echarts.init(container, null, { renderer: "svg" });
									chart.setOption(${JSON.stringify(convertedOptionsWithoutAnimations)});
								})();
							</script>
						</div>
						${explanation && `<i>${explanation}</i>`}
					</div>
				`;
			} catch (error) {
				// eslint-disable-next-line no-console
				console.error("Error processing chart options:", error);
				return '<div class="chart-error">Error processing chart</div>';
			}
		}

		return `<pre><code class="${lang ? `language-${lang}` : ""}">${text}</code></pre>`;
	};

	marked.setOptions({
		renderer: renderer,
	});

	// Process markdown and analyses to combine them
	const issueResponses = splitByHorizontalRule(responseData.response.trim())
		.flatMap((r, i) => {
			// Hide hidden sections
			if (r.trim().length === 0 || hiddenSections.has("issue-" + i)) return [];
			else {
				// Append analysis if available for this section
				return hiddenSections.has("analyses")
					? [r]
					: [
							r +
								(responseData.causeAnalyses && responseData.causeAnalyses![i]
									? responseData.causeAnalyses[i].fullAnalysis + "\n---\n" + responseData.causeAnalyses[i].relevantData
									: ""),
						];
			}
		})
		.map((m) => marked(m) as string);

	const pdfTemplatePath = path.resolve(__dirname, "pdfTemplate.hbs");
	const pdfFooterTemplatePath = path.resolve(__dirname, "pdfPageFooter.hbs");
	const headerImagePath = path.resolve(__dirname, "headerReport.png");
	const logoImagePath = path.resolve(__dirname, "logoCloudfarms.png");
	const fontPath = path.resolve(__dirname, "RedHatText-VariableFont_wght.ttf");

	const allIssuesHidden = issueResponses.length === 0;

	const issueSummaries = hiddenSections.has("summary")
		? null
		: responseData.issues.issues
				.map((issue, idx) => ({
					title: issue.title,
					summary: `${issue.summary} ${responseData.causeAnalyses![idx]?.singleSentenceSummary}`,
					letter: String.fromCharCode(65 + idx),
				}))
				// Only show selected issue in the summary
				// unless all issues are hidden, then show all issues in the summary.
				.filter((_, i) => allIssuesHidden || !hiddenSections.has("issue-" + i));

	const html = renderTemplateFile(pdfTemplatePath, {
		issueResponses: issueResponses,
		farmName: farmInfo.farmName,
		reportDate: reqResData.reportData.reportContext.reportDate
			? formatDateByLanguage(reqResData.reportData.reportContext.reportDate, reqResData.reportData.language)
			: null,
		headerImg: await fileToBase64(headerImagePath),
		fontBase64: await fileToBase64(fontPath),
		issueSummaries,
		allIssuesHidden,
	});

	const ECHARTS_PATH = require.resolve("echarts/dist/echarts.js");

	const browser = await puppeteer.launch({
		args: ["--no-sandbox", "--disable-setuid-sandbox"], // These arguments are needed to run puppeteer in prod
	});
	try {
		const page = await browser.newPage();
		await page.emulateMediaType("print");

		const echartsContent = await fs.readFile(ECHARTS_PATH, "utf8");
		await page.addScriptTag({
			content: echartsContent,
		});

		await page.setContent(html);

		const pdf: Uint8Array = (await page.pdf({
			format: "A4",
			scale: 1, //if you change this, you should also change the width of chart-container parent.
			displayHeaderFooter: true,
			margin: {
				top: "20mm",
				right: "20mm",
				bottom: "30mm",
				left: "20mm",
			},
			headerTemplate: "<div></div>", // Use empty <div></div> for no header",
			footerTemplate: renderTemplateFile(pdfFooterTemplatePath, {
				logoImageUri: await fileToBase64(logoImagePath),
			}),
		})) as Uint8Array; // Keep as Uint8Array for clarity

		return { pdf, reportDate: reqResData.reportData.reportContext.reportDate, farm: farmInfo.farmName };
	} catch (error) {
		logger.error(`Error generating PDF`, error);
		throw new Error("Error generating PDF");
	} finally {
		await browser.close();
	}
}
