<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Efficiency report</title>
	<style>

		body {
			font-family: Arial, sans-serif;
			margin: 0;

		}

		.page {
			box-sizing: border-box; /* **Ensure box-sizing is border-box** */
			position: relative;
			width: 100%;
		}

		.header {
			top: 20mm;
			left: 20mm;
			right: 20mm;
			height: auto;
			text-align: center;
		}

		.header img {
			height: auto;
			width: 100%;
			display: block;
			margin-bottom: 20px;
		}

		.content {
			margin-top: 0;
			margin-bottom: 20mm;
			width: 100%; /* **Explicitly set content width to 100%** */
			text-align: justify;
			-webkit-hyphens: auto;
			-moz-hyphens: auto;
			hyphens: auto;
			box-sizing: border-box; /* Optional, but good practice to be explicit */
			/* Real font size when printed is 12.1px (as seen in firefox).
			This is mismatch is somehow caused by graphs, but 12.1px is good size. */
			font-size: 15px;
		}

		hr { /* Style for page break horizontal rule */
			page-break-after: always; /* Force page break after the hr element */
			border: none;
			height: 0;
			margin: 0;
			padding: 0;
		}

		table {
			width: 100%; /* Make table take full width of its container */
			border-collapse: collapse; /* Single border for table and cells */
			margin-bottom: 20px; /* Add some space below the table */
		}

		th, td {
			border: 1px solid #ddd; /* Light gray border for all cells */
			padding: 8px; /* Comfortable padding within cells */
			text-align: left; /* Left-align text in cells by default */
		}

		th {
			background-color: #f2f2f2; /* Light gray background for header cells */
			font-weight: bold; /* Make header text bold */
			text-align: left; /* Explicitly left-align header text as well */
		}

		/* Basic Markdown styling (you can customize this) */
		h1 {
			font-size: 30px;
		}

		h2 {
			font-size: 20px;
		}

		h3 {
			font-size: 18px;
		}

		p {
			line-height: 1.5;
			margin-bottom: 10px;
		}

		ul, ol {
			margin-bottom: 10px;
			line-height: 1.5;
		}

		li {
			margin-bottom: 5px;
		}

		strong, b {
			font-weight: bold;
		}

		em, i {
			font-style: italic;
		}

		/* Add more Markdown element styles as needed */

	</style>
</head>
<body>
<div class="page">
	<div class="header">
		<img src="{{headerImg}}" alt="virtual farm assistant report header">
	</div>
	<div class="content">
		<div style="padding-bottom: 10px;">
			<h1>{{farmName}}</h1>
			{{#if reportDate}}{{reportDate}}{{/if}}
		</div>
		{{#if issueSummaries}}
			<h3>Summary</h3>
			<ul>
				{{#each issueSummaries}}
					{{#if @root.allIssuesHidden}}
						<li><strong>{{this.letter}}. {{this.title}}</strong>: {{this.summary}}</li>
					{{else}}
						<li><a href="#issue-{{@index}}"><strong>{{this.letter}}. {{this.title}}</strong></a>: {{this.summary}}</li>
					{{/if}}
				{{/each}}
			</ul>
			<hr />
		{{/if}}
		{{#each issueResponses}}
			<div id="issue-{{@index}}">
				{{{this}}}
			</div>
			{{#unless @last}}
				<hr />
			{{/unless}}
		{{/each}}
	</div>
</div>
</body>
</html>