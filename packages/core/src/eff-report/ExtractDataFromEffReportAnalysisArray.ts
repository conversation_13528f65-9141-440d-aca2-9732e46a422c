import {
	CauseAnalysisCustomerResponse,
	EffReportSummaryResponseChunk,
	EffReportSummaryStep,
	EffReportSummaryStepValues,
} from "./EffReportSummaryResponseChunk";
import { match, P } from "ts-pattern";
import type { FarmTimelineEntity } from "../cf-link/CfLinkActivities";
import { IdentifyIssuesResponse } from "./IdentifyIssuesResponseSchema";
import { Usage } from "../llm/LlmCommon";
import { hash } from "ohash";

type Pending = undefined;
const Pending: Pending = undefined;

/**
 * Extract data from the stream.
 * This is used both on the frontend and backend.
 */
export function extractDataFromEffReportAnalysisArray(streamArray: EffReportSummaryResponseChunk[]) {
	return streamArray.reduce(
		(acc, chunk) => {
			return match(chunk)
				.with({ type: "step" }, ({ step }) => ({
					...acc,
					currentStep:
						EffReportSummaryStepValues.indexOf(step) > EffReportSummaryStepValues.indexOf(acc.currentStep) ? step : acc.currentStep,
				}))
				.with({ type: "identifiedIssues" }, (data) => ({ ...acc, issues: data.issues }))
				.with({ type: "usage" }, (usage) => ({
					...acc,
					usage,
				}))
				.with({ type: "responseId" }, ({ responseId }) => ({
					...acc,
					responseId,
				}))
				.with(P.string, (messageChunk) => ({
					...acc,
					response: (acc.response ?? "") + messageChunk,
				}))
				.with({ type: "causesAnalyses" }, (data) => {
					return {
						...acc,
						causeAnalyses: data.causesAnalyses,
					};
				})
				.with({ type: "farmTimeline" }, ({ farmTimeline }) => ({ ...acc, farmTimeline }))
				.with({ type: "conflictingGoals" }, ({ conflictingGoals }) => ({
					...acc,
					conflictingGoals: conflictingGoals
						? {
								conflictingGoals,
								hash: hash(conflictingGoals),
							}
						: null,
				}))
				.exhaustive();
		},
		{
			currentStep: EffReportSummaryStepValues[0] as EffReportSummaryStep,
			usage: null as Usage | null,
			responseId: null as string | null,
			response: Pending as Pending | string,
			issues: null as IdentifyIssuesResponse | null,
			causeAnalyses: null as CauseAnalysisCustomerResponse[] | null,
			farmTimeline: Pending as FarmTimelineEntity[] | Pending,
			conflictingGoals: Pending as
				| {
						conflictingGoals: string[];
						hash: string;
				  }
				| null
				| Pending,
		},
	);
}
