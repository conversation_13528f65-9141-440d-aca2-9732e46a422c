import type { EffReportInternalChunk } from "./EfficiencyReportService";
import { match } from "ts-pattern";
import { StreamedResponseWithIdChunk, stripUnauthorizedChunksFromStream } from "./StreamedResponseWithIdChunk";
import type { FarmTimelineEntity } from "../cf-link/CfLinkActivities";
import { IdentifyIssuesResponse } from "./IdentifyIssuesResponseSchema";
import { UserContext } from "../UserContext";

export type CauseAnalysisCustomerResponse = { fullAnalysis: string; relevantData: string; singleSentenceSummary: string };
/**
 * Chunk of streamed response containing only data needed to show progress and response to the customer.
 */
export type EffReportSummaryResponseChunk =
	| {
			type: "step";
			step: EffReportSummaryStep;
	  }
	| {
			type: "identifiedIssues";
			issues: IdentifyIssuesResponse;
	  }
	| {
			type: "causesAnalyses";
			/**
			 * The order of analyses corresponds to the order of identified issues.
			 * Each string is a markdown formatted text.
			 */
			causesAnalyses: CauseAnalysisCustomerResponse[];
	  }
	| {
			type: "farmTimeline";
			farmTimeline: FarmTimelineEntity[];
	  }
	| {
			type: "conflictingGoals";
			conflictingGoals: string[] | null;
	  }
	| StreamedResponseWithIdChunk;
/**
 * Steps of the eff report summary generation
 */
export const EffReportSummaryStepValues = ["IdentifyingIssues", "getRelevantData", "AnalyzeCauses", "Summarizing"] as const;
/**
 * Type of single step
 */
export type EffReportSummaryStep = (typeof EffReportSummaryStepValues)[number];

/**
 * Converts internal response chunk shown on prompt management screen to
 * a response chunk shown to customers on eff report screen.
 * Internal data are omitted and only overview of progress is left.
 */
export function internalChunk2CustomerChunk(
	internalResponse: EffReportInternalChunk,
	stripUnauthorizedChunksForRole: UserContext["role"] | false,
): EffReportSummaryResponseChunk[] {
	return (
		match(internalResponse)
			.with({ type: "introduction" }, () => [
				{
					type: "step",
					step: "IdentifyingIssues",
				} as const,
			])
			.with({ type: "farmInfo" }, (data) => [
				{
					type: "farmTimeline",
					farmTimeline: data.farmInfo.farmTimeline,
				} as const,
			])
			.with({ type: "identifyIssuesResponse" }, (data) => [
				{
					type: "step",
					step: "getRelevantData",
				} as const,
				{
					type: "identifiedIssues" as const,
					issues: data.identifyIssuesResponse.response,
				},
			])
			.with({ type: "getRelevantData" }, () => [
				{
					type: "step",
					step: "AnalyzeCauses",
				} as const,
			])
			.with(
				{
					type: "analyzeCausesResponse",
				},
				(value) => [
					{
						type: "causesAnalyses" as const,
						causesAnalyses: value.analyzeCausesResponse.map((c) => ({
							fullAnalysis: c.analysis,
							relevantData: c.usedData,
							singleSentenceSummary: c.summary,
						})),
					},
					{
						type: "step",
						step: "Summarizing",
					} as const,
				],
			)
			.with({ type: "conflictingGoals" }, (value) => [
				{
					type: "conflictingGoals",
					conflictingGoals: value.conflictingGoals,
				} as const,
			])
			.with({ type: "summarizationMetadata" }, () => [])
			// Type is explicitly stated to catch missing cases in compile time
			.otherwise((response: StreamedResponseWithIdChunk) => stripUnauthorizedChunksFromStream(response, stripUnauthorizedChunksForRole))
	);
}
