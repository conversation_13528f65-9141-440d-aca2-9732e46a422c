Your task is to identify conflicts within a given set of goals and their corresponding minimum/maximum ranges for Key Performance Indicators (KPIs) on a pig farm.

First, understand how a KPI's goal is considered achieved:
- For some KPIs, achievement means the actual value is as close as possible to the target goal.
- For other KPIs, a value equal to or higher than the goal is considered successful (higher is better).
- And for some, a value equal to or lower than the goal signifies achievement (lower is better).

We define a KPI as having a "good value" when its specific goal, as defined above, is met.

A conflict exists if a primary KPI's goal is unachievable. This means there's no scenario where its component KPIs can all have "good values" that would allow the primary KPI to achieve its goal. Conversely, if at least one combination of "good values" for component KPIs allows the primary KPI to achieve its goal, there is no conflict.

Ignore conflicts caused by DAYS_COUNT.

Create a bullet point for each unique conflict with a short explanation. In the explanation show the values, how things are calculated and explain why it is a conflict. Here is an example of such bullet point:
**Conflict between weekly service components and total weekly services:** The goal for `Services / week [Wₙ]` (total services) is 50. The goal for `Gilts served per week [Wₙ]` is 11, and the goal for `Sows served per week [Wₙ]` (non-gilt services) is 50. Based on the formulas, total weekly services should approximately equal the sum of gilt services and sow services. However, the goals lead to a conflict: `11 + 50 = 61`, which is not equal to the total services goal of `50`.
// End of example

Reference to KPIs using their names. Never show the codes.

In your response only write the conflicts in bullet points. Don't add any comments before or after.

Respond in the following language: {{language}}.
The response should use terminology understandable to pig farmer from {{country}}.

If there are no conflicts, only write NO_CONFLICTS in your response.

{{kpiGoalsText}}