You will be given a list of issues identified on a pig farm.
Your goal is to list the issues together with actions to solve them.
Respond in the following language: {{language}}.
The response should use terminology understandable to pig farmer from {{country}}.

In your response list all issues from "Identified issues" section. Don't omit any!

Make a header for each issue from "Identified issues". The header is located in "Issue title".

Below the header add the following bullet points for each issue:
1. Issue:
Include the full content and formatting of section "Issue description", except for the headers that must be removed. Don't change anything else.

2. Cause:
Include the full content and formatting of section "Cause of the issue", except for the headers that must be removed. Don't change anything else.

3. Action:
List up to 3 most effective action(s) that can be performed to resolve the cause of the issue.
Each action must be a sub-bullet point of the parent "3. Action" bullet point. Make the action header bold. Summarize the action and explain how the action will resolve the cause.
{{#if SOPsExist}}
An action can either be based on SOP or it can be a generic advice to focus on some area.
If there is no SOP that can be used to solve the issue, suggest an area to focus on.
If there is a SOP that can be used to solve the issue, use activities described in the SOP to suggest a concrete action. {{SOPLinkFormatPrompt}}
If there are specific numbers recommended for an action in the SOPs please list those numbers.
{{/if}}
{{#unless SOPsExist}}
The action should not be a concrete action but a suggestion on what to focus on.
{{/unless}}

Put a horizontal line below the bullet points to separate issues. Don't use one horizontal lines anywhere else.

In your response don't add anything before or after the list.

{{WriteInStandardLanguageLevel}}

Identified issues:
{{#each analyzedIssues}}
- {{this.letter}}. {{this.issue.title}}
{{/each}}

{{#each analyzedIssues}}
Issue title: {{this.letter}}. {{this.issue.title}}
Issue description:
{{this.issue.description}}
End: Issue description

Cause of the issue:
{{this.cause}}
End: Cause of the issue

Cause analysis:
{{this.analysis}}
End: Cause analysis

End: Issue title: {{this.letter}}. {{this.issue.title}}

{{/each}}

{{#if holdingInstructions}}
{{holdingInstructions}}
{{/if}}

{{#if SOPs}}
Farm Standard Operating Procedures (SOPs):
{{SOPs}}
End of SOPs.
{{/if}}

{{#if segesManual}}
Standard Operating Procedures (SOPs) from SEGES manual:
{{segesManual}}
End of SEGES manual.
{{/if}}

{{#if southWestVetsSOPs}}
South West Vets Standard Operating Procedures (SOPs):
{{southWestVetsSOPs}}
End of South West Vets SOPs.
{{/if}}