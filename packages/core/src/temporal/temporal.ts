import Config from "../Config";
import { Client, Connection, WorkflowNotFoundError } from "@temporalio/client";

import { Duration } from "@temporalio/common";
import { NativeConnection } from "@temporalio/worker";

const temporalOptions = {
	address: Config.TEMPORAL_ADDRESS,
	// Include tls section if https is enabled
	...(Config.TEMPORAL_TLS
		? {
				tls: {
					serverNameOverride: Config.TEMPORAL_ADDRESS.split(":")[0],
				},
			}
		: {}),
	metadata: { authorization: Config.TEMPORAL_JWT_TOKEN },
	connectTimeout: 60_000, //worker had trouble to start, failed on grpc error. This helped (default 10s)
};

export const temporalClient: Promise<Client> = getTemporalClient();

export async function getTemporalNativeConnection() {
	return await NativeConnection.connect(temporalOptions);
}

async function getTemporalClient() {
	const connection = await Connection.connect(temporalOptions);

	return new Client({
		connection,
		namespace: Config.TEMPORAL_NAMESPACE,
	});
}

export async function startOrAwaitWorkflow<A extends unknown[], T>(
	workerQueue: string,
	workflowId: string,
	workflowFunction: (...args: A) => Promise<T>,
	args: A,
	workflowRunTimeout?: Duration,
): Promise<T> {
	const client = await temporalClient;
	try {
		const oldWorkflow = client.workflow.getHandle(workflowId);
		const oldWorkflowDescription = await oldWorkflow.describe();
		if (oldWorkflowDescription.status.name === "RUNNING") {
			//Workflow is already running, wait for it to finish and return the result.
			return await oldWorkflow.result();
		}
	} catch (e: unknown) {
		if (e instanceof WorkflowNotFoundError) {
			//Workflow was not yet run ever before
		} else {
			throw e;
		}
	}

	//Either workflow was not yet run, or it has already finished. New workflow should be run.
	const handle = await client.workflow.start(workflowFunction, {
		taskQueue: workerQueue,
		args: args,
		workflowId: workflowId,
		workflowIdReusePolicy: "ALLOW_DUPLICATE",
		// Timeout in 45 seconds in tests because we usually give 60s timeout for a single tests,
		// otherwise use the provided value
		workflowRunTimeout: Config.NODE_ENV === "test" ? "45s" : workflowRunTimeout,
	});

	return await handle.result();
}

export const PIGBOT_WORKER_QUEUE = Config.TEMPORAL_WORKER_QUEUE;
