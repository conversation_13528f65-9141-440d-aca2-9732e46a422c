import "../../test/jest.setup";
import { expect } from "@jest/globals";
import { SystemHoldingId } from "../HoldingId";
import type { KpiGoalData } from "../cf-link/CfLinkActivities";

test(
	"detectConflictingGoals should detect conflicts between KPI goals",
	async () => {
		const module = await import("./DataQualityService");

		// Sample KPI goals data with potential conflicts
		const kpiGoals: KpiGoalData[] = [
			{
				code: "DEAD_BORNS_PER_LITTER",
				name: "Stillborn per litter [№]",
				formula: "DEAD_BORNS / FARROWINGS",
				description:
					"Calculates the average number of dead born piglets per litter for sows in the given reporting period. This metric helps in assessing the health and management of breeding practices by providing insights into the number of piglets that did not survive birth across all litterings reported within the specified interval.",
				goal: 1.5,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "FARROWING_RATE",
				name: "Farrowing rate [%]",
				formula: "100 * FARROWING_RATE_FARROWINGS / FARROWING_RATE_SERVINGS",
				description:
					"Farrowing rate measures the success of inseminations resulting in farrowings. It calculates the proportion of successful farrowings to the total number of servings within a specific time frame, adjusted by the farm's settings for gestation period. This KPI gives insights into the reproductive efficiency and potential areas for management improvements.",
				goal: 90,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR1_FARROWING_RATE",
				name: "Farrowing rate of first farrowing sows [%]",
				formula: "100 * PAR1_FARROWING_RATE_FARROWINGS / PAR1_FARROWING_RATE_SERVINGS",
				description:
					"This metric measures the farrowing success of first parity sows by comparing the number of successful farrowings to the number of servings attempted within the specified reporting period, adjusted for the expected gestation period. The calculation considers only those servings not excluded by farm-specific criteria, such as sales or purchases of pregnant sows.",
				goal: 90,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "LIVE_BORNS_PER_LITTER",
				name: "Liveborn per litter [№]",
				formula: "LIVE_BORNS / FARROWINGS",
				description:
					"Calculates the average number of liveborn piglets per sow litter during the reporting period. This metric considers data from farrowings or servings within the specified timeframe, depending on whether service group parameters are applied. It provides a direct insight into the reproductive performance and efficiency of the sows.",
				goal: 19.5,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "REPEAT_SERVINGS_PER_WEEK",
				name: "Repeat services per week [№]",
				formula: "7 * REPEAT_SERVINGS / DAYS_COUNT",
				description:
					"Captures the average frequency of re-servings occurring each week within the reporting period. It provides insight into how often servings are repeated over time.",
				goal: 2,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR1_FARROWINGS_PER_WEEK",
				name: "Parity 1 farrowings per week [№]",
				formula: "7 * PAR1_FARROWINGS / DAYS_COUNT",
				description:
					"Calculates the average number of sows reaching parity 1 that farrow each week for a specified reporting interval. The metric provides insights into the weekly farrowing trends for first-time sows, contributing to an understanding of overall herd productivity.",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR0_FIRST_SERVING_AGE",
				name: "Age at first service [days]",
				formula: "PAR0_SERVING_AGE / PAR0_KNOWN_AGE_SERVINGS",
				description:
					"This indicator represents the age in days when gilts are first served, based on available data within the specified reporting period. It calculates the average initial serving age by considering the age of each gilt at the time of her first service.",
				goal: 225,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "SERVINGS_PER_WEEK",
				name: "Services / week [№]",
				formula: "7 * SERVINGS / DAYS_COUNT",
				description:
					"Calculates the average number of servings distributed each week within the report interval. This KPI considers both initial servings and repeated servings through the entire reporting period and is designed to provide a comprehensive weekly overview of serving trends and frequency.",
				goal: 50,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR0SOW_DEAD_RATE",
				name: "Sows mortality rate [%/year]",
				formula: "(SOWS_DEAD_AMOUNT / DAYS_COUNT) * 365 * 100 / AVERAGE_SOW_AMOUNT",
				description:
					"The proportion of sows, including inseminated gilts, that died within the reporting interval, standardized over the entire year and expressed as a percentage of the average sow population during that interval.",
				goal: 10,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR1_FARROWINGS",
				name: "Parity 1 farrowings [№]",
				formula: "PAR1_FARROWINGS",
				description:
					"Provides the number of farrowings of sows in report interval.\nLate abortions are not included if setting `treatZeroLiveBornAsAbortion` is set and the number of liveborn is zero.\nData are taken from either farrowings that match report interval or servings that match report interval\nbased on the `useServiceGroups` parameter.\n",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR1_FARROWINGS_PCT",
				name: "Parity 1 farrowings [%]",
				formula: "100 * PAR1_FARROWINGS/FARROWINGS",
				description:
					"Represents the percentage of sows achieving their first parity within the report interval. Excludes late abortions if the 'treatZeroLiveBornAsAbortion' setting is activated and no liveborn piglets are recorded. This figure is derived from either the farrowings aligning with the specified period or the services within this timeframe, contingent upon the 'useServiceGroups' setting.",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "GILT_SERVINGS_PER_WEEK",
				name: "Gilts served per week [№]",
				formula: "7 * GILT_SERVINGS / DAYS_COUNT",
				description:
					"This KPI measures the weekly average number of first-time servings for gilts during the specified reporting period. It helps assess breeding activity efficiency and planning by calculating how many gilts are served on a weekly basis throughout the reporting interval.",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "NPD_PER_LITTER",
				name: "Non-productive days per litter",
				formula:
					"context$breed ? ((365 * SOWS_NPD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * SOWS_NPD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)",
				description:
					"Provides the average of non-productive days of sow per litter. Sow is considered productive when it is pregnant or lactating. Non-productive days are between last weaning and next insemination, dead or selling for slaughter and between insemination and some kind of failure like failed insemination, abortion, dead or sold for slaughter. Weaning day is considered as a productive day based on farm setting `Is weaning a productive day`.",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "LACTATION_DAYS",
				name: "Lactation period [days]",
				formula: "SUM_LACTATION_DAYS / WEANED_SOWS",
				description:
					"Calculates the average number of days a sow spends in lactation, from farrowing to regular weaning of her piglets. This measure can be filtered by specific breeds when applicable.",
				goal: 32,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "AVERAGE_SOW_AMOUNT",
				name: "Avg. number of active sows [№]",
				formula: "context$breed ? (SOWS_REAL_FEEDING_DAYS / DAYS_COUNT) : (SOWS_FEEDING_DAYS / DAYS_COUNT)",
				description:
					"This KPI calculates the average number of sows present during the reporting interval. The calculation considers whether a specific breed filter is applied. If the breed filter is active, the total days sows were fed and alive for various parities is divided by the report days. If not, a broader calculation including additional feeding days for inseminated gilts is used.",
				goal: 1050,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "GILTS_OF_ALL_FIRST_SERVICES_US",
				name: "% gilts of all first services",
				formula: "(GILT_SERVINGS/(SERVINGS-REPEAT_SERVINGS))*100",
				description:
					"This KPI tallies the total number of initial service events involving gilts during the reporting period. It focuses on maiden services for young female pigs.",
				goal: 21,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "FARROWINGS_PER_WEEK",
				name: "Farrowings / week [№]",
				formula: "7 * FARROWINGS / DAYS_COUNT",
				description:
					"Provides the average number of sow farrowings occurring weekly within the specified report interval. This metric helps in understanding the frequency of sow deliveries over a given time frame. It typically considers valid farrowings based on predefined parameters set for the farm operations.",
				goal: 45,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "SOW_SERVINGS_PER_WEEK",
				name: "Sows served per week [№]",
				formula: "7 * REGULAR_SERVINGS / DAYS_COUNT",
				description:
					"This KPI calculates the average number of sow servings completed each week within the given reporting interval. It evaluates both first-time servings and any subsequent servings during the period, providing insight into the weekly breeding operations and cycle regularity in a sow farm.",
				goal: 50,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "LITTER_SOW_YEAR",
				name: "Litter / sow / year [№]",
				formula:
					"(settings$litter_sow_year_algorithm == 2 && isDefined(DAYS_COUNT)) ? SOW_FARROWING_BASED_LITTER_SOW_YEAR : (settings$litter_sow_year_algorithm == 1 ? SOW_NPD_BASED_LITTER_SOW_YEAR : SOW_CYCLE_BASED_LITTER_SOW_YEAR)",
				description:
					"This key performance indicator estimates the annual litter production per sow. It accounts for various calculation methods, including utilizing non-productive days, farrowing data or the sow's reproductive cycle, depending on configured algorithms. The approach dynamically adapts based on settings, to reflect realistic sow productivity within the assessment timeframe.",
				goal: 2.33,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "REPEAT_SERVINGS_RATIO",
				name: "Returner rate [%]",
				formula: "100 * REPEAT_SERVINGS / SERVINGS",
				description:
					"The repeat servings ratio indicates the proportion of total servings that were re-servings during a reporting period, offering insight into the effectiveness of initial servings.",
				goal: 5,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "FEMALES_DEADYEAR",
				name: "Females dead/year [%]",
				formula: "(SOWS_GILTS_DEAD_AMOUNT/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(365.25/DAYS_COUNT)*100",
				description:
					"This KPI reflects the annual mortality rate of breeding females relative to their average population size over the reporting period. It considers the proportion of deceased sows and gilts compared to the average number of females over time, then scales this figure to a yearly basis, accounting for varying interval lengths, and expresses it as a percentage.",
				goal: 10,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "SOW_DEAD_AMOUNT_PER_WEEK",
				name: "Dead sows per week [№]",
				formula: "7 * SOW_DEAD_IND_ANIMALS_AMOUNT / DAYS_COUNT",
				description:
					"Calculate the weekly average of deceased sows during the reporting period, excluding sows with a parity of zero (inseminated gilts).",
				goal: 3,
				rangeFrom: null,
				rangeTo: null,
			},
		];
		// Call the function with the test data
		const result = await module.detectConflictingGoals(
			kpiGoals,
			SystemHoldingId,
			{
				context: {
					country: "US",
					language: "en",
				},
				content: "You are a virtual pig farm assistant. The farm is located in USA. Respond in following language: en.",
				template: {
					template: "IGNORED",
					id: "IGNORED",
				},
			},
			undefined,
		);

		// Verify the result is not null (conflicts were detected)
		expect(result.response).not.toBeNull();

		// Since we've verified response is not null, we can safely use it
		if (result.response) {
			// Verify the response is an array with at least three items
			expect(Array.isArray(result.response)).toBe(true);
			expect(result.response.length).toBeGreaterThanOrEqual(3);
		}
	},
	5 * 60000, // 5 minute timeout
);

test(
	"detectConflictingGoals should return null when no conflicts are detected",
	async () => {
		const module = await import("./DataQualityService");

		// Sample KPI goals data with two unrelated KPIs that shouldn't have conflicts
		const kpiGoals: KpiGoalData[] = [
			{
				code: "SOW_DEAD_AMOUNT_PER_WEEK",
				name: "Dead sows per week [№]",
				formula: "7 * SOW_DEAD_IND_ANIMALS_AMOUNT / DAYS_COUNT",
				description:
					"Calculate the weekly average of deceased sows during the reporting period, excluding sows with a parity of zero (inseminated gilts).",
				goal: 3,
				rangeFrom: null,
				rangeTo: null,
			},
			{
				code: "PAR1_FARROWINGS",
				name: "Parity 1 farrowings [№]",
				formula: "PAR1_FARROWINGS",
				description:
					"Provides the number of farrowings of sows in report interval.\nLate abortions are not included if setting `treatZeroLiveBornAsAbortion` is set and the number of liveborn is zero.\nData are taken from either farrowings that match report interval or servings that match report interval\nbased on the `useServiceGroups` parameter.\n",
				goal: 11,
				rangeFrom: null,
				rangeTo: null,
			},
		];

		// Call the function with the real LLM
		const result = await module.detectConflictingGoals(
			kpiGoals,
			SystemHoldingId,
			{
				context: {
					country: "US",
					language: "en",
				},
				content: "You are a virtual pig farm assistant. The farm is located in USA. Respond in following language: en.",
				template: {
					template: "IGNORED",
					id: "IGNORED",
				},
			},
			undefined,
		);

		// If the LLM correctly identifies that there are no conflicts, result.response should be null
		// If the LLM incorrectly identifies conflicts, this test will fail
		expect(result.response).toBeNull();
	},
	5 * 60000, // 5 minute timeout
);
