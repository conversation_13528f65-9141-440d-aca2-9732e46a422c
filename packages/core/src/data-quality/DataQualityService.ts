import { KpiGoalData } from "../cf-link/CfLinkActivities";
import { textCompletion } from "../llm/LlmCommon";
import { RenderedPromptTemplate, renderPromptTemplate } from "../prompt-template/PromptTemplate";
import { HoldingId, SystemHoldingId } from "../HoldingId";
import { splitMarkdownListToArray } from "../utils/markdownUtils";
import { DataQualityPrompts } from "./DataQualityPrompts";
import { PromptCustomization } from "../PromptCustomization";

export async function detectConflictingGoals(
	kpiGoals: KpiGoalData[],
	holdingId: HoldingId,
	introductionResponse: RenderedPromptTemplate,
	promptCustomization?: PromptCustomization,
) {
	if (!kpiGoals || kpiGoals.length === 0) {
		return {
			response: null,
		};
	}

	// TODO cache this in the database so that these goals don't ever change (not just 7 days caching)
	const response = await textCompletion({
		messages: [
			{
				role: "system",
				content: introductionResponse.content,
			},
			{
				role: "user",
				content: (
					await renderPromptTemplate({
						promptId: {
							promptType: DataQualityPrompts.detectConflictingGoal,
							holdingId: SystemHoldingId,
							templateOverrides: promptCustomization?.templateOverrides,
						},
						context: {
							...introductionResponse.context,
							kpiGoalsText: JSON.stringify(kpiGoals, null, 2),
						},
					})
				).content,
			},
		],
		metricData: {
			context: holdingId,
			action: "detect-conflicting-goal",
			activity: "eff-report",
		},
		cacheIdx: promptCustomization?.cacheIdx,
	});

	const noConflicts = response.response.includes("NO_CONFLICTS");

	return {
		...response,
		response: noConflicts ? null : splitMarkdownListToArray(response.response),
	};
}
