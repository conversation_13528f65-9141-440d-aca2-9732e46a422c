import "pigbot-core/test/jest.setup";
import { expect } from "@jest/globals";
import { splitMarkdownListToArray } from "./markdownUtils";

describe("splitMarkdownListToArray", () => {
	test("should split markdown list with bullet points (•)", () => {
		const markdown = `
• First item
• Second item
• Third item with more text
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual(["First item", "Second item", "Third item with more text"]);
	});

	test("should split markdown list with asterisks (*)", () => {
		const markdown = `
* First item
* Second item
* Third item with more text
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual(["First item", "Second item", "Third item with more text"]);
	});

	test("should split markdown list with hyphens (-)", () => {
		const markdown = `
- First item
- Second item
- Third item with more text
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual(["First item", "Second item", "Third item with more text"]);
	});

	test("should handle multi-line items", () => {
		const markdown = `
- First item
  with continuation
- Second item
  with multiple
  lines of continuation
- Third item
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual(["First item with continuation", "Second item with multiple lines of continuation", "Third item"]);
	});

	test("should handle mixed bullet point styles", () => {
		const markdown = `
• First item
* Second item
- Third item
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual(["First item", "Second item", "Third item"]);
	});

	test("should handle empty input", () => {
		expect(splitMarkdownListToArray("")).toEqual([]);
		expect(splitMarkdownListToArray(null)).toEqual([]);
		expect(splitMarkdownListToArray(undefined)).toEqual([]);
	});

	test("should handle input with no bullet points", () => {
		const markdown = `
This is just regular text
with no bullet points.
`;
		const result = splitMarkdownListToArray(markdown);
		expect(result).toEqual([]);
	});

	test("should handle nested lists by treating them as continuation", () => {
		const markdown = `
- First item
  - Nested item 1
  - Nested item 2
- Second item
`;
		const result = splitMarkdownListToArray(markdown);
		// For now, we're just checking that all the text content is present
		// in the result, not necessarily in the exact format
		expect(result.join(" ")).toContain("First item");
		expect(result.join(" ")).toContain("Nested item 1");
		expect(result.join(" ")).toContain("Nested item 2");
		expect(result.join(" ")).toContain("Second item");
	});
});
