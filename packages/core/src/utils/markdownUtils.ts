/**
 * Utility functions for working with markdown content
 */

/**
 * Splits markdown text by horizontal rules (--- or ***)
 * @param markdown The markdown text to split
 * @returns An array of markdown sections
 */
export function splitByHorizontalRule(markdown: string): string[] {
	// Split by both --- and *** followed by newline
	const parts = markdown.split(/---\n|\*\*\*\n/);
	// Filter out empty sections
	return parts.filter((part) => part.trim().length > 0);
}

/**
 * Removes the first heading from a markdown text
 * @param markdown The markdown text to process
 * @returns The markdown text without the first heading
 */
export function removeHeading(markdown: string): string {
	// Remove any leading newlines, followed by a heading (#), followed by any text until a newline, followed by one or more newlines
	return markdown.replace(/^\n*#[^\n]*\n+/, "");
}

/**
 * Splits a markdown string into an array where each item corresponds to a list item.
 * Handles bullet points (•), asterisks (*), and hyphens (-) as list markers.
 * Treats nested lists and multi-line items as continuation of the parent item.
 *
 * @param markdownText The markdown text containing a list
 * @returns An array of strings, each representing a list item with markers removed
 */
export function splitMarkdownListToArray(markdownText: string | null | undefined): string[] {
	if (!markdownText) return [];

	const lines = markdownText.split("\n");
	const result: string[] = [];
	let currentItem: string | null = null;
	let isInList = false;

	for (const line of lines) {
		const trimmedLine = line.trim();
		const isListItem = trimmedLine.startsWith("•") || trimmedLine.startsWith("* ") || trimmedLine.startsWith("- ");

		if (isListItem) {
			// If we were already building an item, push it to results
			if (currentItem !== null) {
				result.push(currentItem);
			}

			// Start a new item, removing the list marker
			currentItem = trimmedLine.replace(/^[•*-]\s+/, "");
			isInList = true;
		} else if (isInList && trimmedLine && currentItem !== null) {
			// This is a continuation of the current item
			currentItem += " " + trimmedLine;
		}
	}

	// Don't forget to add the last item if there is one
	if (currentItem !== null) {
		result.push(currentItem);
	}

	return result;
}
