import dotenv from "dotenv";
import path from "path";
import ms from "ms";
import { z } from "zod";

function initialize() {
	const envVariant = process.env.NODE_ENV ?? "development";

	dotenv.config({
		path: [
			path.resolve(__dirname, `../.env.${envVariant}.local`),
			path.resolve(__dirname, `../.env.${envVariant}`),
			path.resolve(__dirname, `../.env`),
		],
	});
}

initialize();

const configSchema = z.object({
	NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
	OPENAI_API_KEY: z.string(),
	OPENAI_ASSISTANT_ID: z.string(),
	COMPLETION_CACHE_TTL: z.string().transform((val) => ms(val)! / 1000),
	CHATGPT4O_TPM: z.string().transform((val) => parseInt(val)),
	BACKEND_PORT: z.string(),
	SHARED_REDIS_HOST: z.string(),
	SHARED_REDIS_PORT: z.string().transform((val) => parseInt(val)),
	SHARED_REDIS_PASS: z.string(),
	LOCAL_REDIS_HOST: z.string().optional(),
	LOCAL_REDIS_PORT: z
		.string()
		.transform((val) => parseInt(val))
		.optional(),
	PG_HOST: z.string(),
	PG_PORT: z.string().transform((val) => parseInt(val)),
	PG_USER: z.string(),
	PG_PASSWORD: z.string(),
	PG_DATABASE: z.string(),
	WORKER_DIR: z.string(),
	JWT_SECRET: z.string(),
	SLACK_BOT_TOKEN: z.string().optional(),
	DIXA_JWT: z.string(),
	DIXA_TOKEN: z.string(),
	ENABLE_WIP: z.string().transform((v) => v === "true"),
	TEMPORAL_ADDRESS: z.string(),
	TEMPORAL_NAMESPACE: z.string(),
	TEMPORAL_TLS: z
		.string()
		.default("false")
		.transform((x) => x === "true"),
	TEMPORAL_JWT_TOKEN: z.string(),
	TEMPORAL_WORKER_QUEUE: z.string().default("worker"),
	SUPPORT_CHATBOT_THREAD_CACHE_TTL: z.string().transform((val) => ms(val)! / 1000),
	GEMINI_API_KEY: z.string(),
});

const parsed = configSchema.parse(process.env);

export default parsed;

export function refreshConfig() {
	Object.assign(parsed, configSchema.parse(process.env));
}

export async function getConfig() {
	return parsed;
}

export type Config = typeof parsed;
