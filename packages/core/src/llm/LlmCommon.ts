import { MetricData } from "../cost-metrics";
import { SimpleChatMessage } from "../eff-report/SimpleChatMessage";
import { streamCompletionChatGpt, structuredCompletionChatGpt, textCompletionChatGpt } from "./ChatGptClient";
import {
	GEMINI_FLASH_MODEL,
	GEMINI_PRO_MODEL,
	streamCompletionGemini,
	structuredCompletionGemini,
	textCompletionGemini,
} from "./GeminiClient";
import logger from "../logger";
import { z } from "zod";

/**
 * This allows disabling caching of responses in development
 */
export const ResponseCacheEnabled = process.env["CACHE_RESPONSES"] !== "false";

if (!ResponseCacheEnabled) {
	logger.warn("Response caching is disabled");
}

/**
 * Common parameters for text completion across different LLM providers
 */
export type LlmProvider = "chatgpt" | "gemini-pro" | "gemini-flash";

/**
 * Mapping of LLM provider to model name
 */
export const GeminiVariantToModel: Record<Exclude<LlmProvider, "chatgpt">, string> = {
	"gemini-pro": GEMINI_PRO_MODEL,
	"gemini-flash": GEMINI_FLASH_MODEL,
};

export type CommonCompletionParams = {
	/** The LLM provider to use (defaults to "gemini-pro") */
	llm?: LlmProvider;
	/** The messages to send to the LLM */
	messages: SimpleChatMessage[];
	/** Metric data for tracking usage */
	metricData: MetricData;
	/** Cache index for response caching. Set to undefined to disable caching */
	cacheIdx?: number;
};

export type MessageChunk = string;
export type Usage = {
	type: "usage";
	metricData: MetricData;
	inputCost: number;
	outputCost: number;
	inputTokens: number;
	cachedTokens: number;
	outputTokens: number;
};

export function emptyUsage(metricData: MetricData): Usage {
	return {
		type: "usage",
		metricData: metricData,
		inputCost: 0,
		outputCost: 0,
		inputTokens: 0,
		cachedTokens: 0,
		outputTokens: 0,
	};
}

export function sumUsages(usage1: Usage, usage2: Usage): Usage {
	return {
		type: "usage",
		metricData: {
			...usage1.metricData,
		},
		inputCost: usage1.inputCost + usage2.inputCost,
		outputCost: usage1.outputCost + usage2.outputCost,
		inputTokens: usage1.inputTokens + usage2.inputTokens,
		cachedTokens: usage1.cachedTokens + usage2.cachedTokens,
		outputTokens: usage1.outputTokens + usage2.outputTokens,
	};
}

export type ResponseHash = {
	type: "responseHash";
	hash: string;
};
export type ResponseWithUsage<T> = {
	response: T;
	usage?: Usage;
};
/**
 * Chunks of a streamed text and metadata from ChatGPT.
 */
export type StreamedResponseChunk = MessageChunk | Usage | ResponseHash;

/**
 * A unified text completion function that works with multiple LLM providers.
 *
 * @param params The parameters for the text completion
 * @returns A promise that resolves to the response with usage information
 */
export async function textCompletion(params: CommonCompletionParams): Promise<ResponseWithUsage<string>> {
	const { llm = "gemini-pro", messages, metricData, cacheIdx = 0 } = params;

	switch (llm) {
		case "chatgpt":
			return textCompletionChatGpt({
				messages,
				metricData,
				cacheIdx,
				responseFinishedCheck: true,
			});

		case "gemini-pro":
		case "gemini-flash":
			return textCompletionGemini({
				...transformMessages(messages),
				cacheIdx,
				model: GeminiVariantToModel[llm],
				metricData,
			});

		default:
			throw new Error(`Unsupported LLM provider: ${llm}`);
	}
}

/**
 * A unified streaming text completion function that works with multiple LLM providers.
 *
 * @param params The parameters for the streaming text completion
 * @returns An async generator that yields response chunks
 */
export async function* streamCompletion(params: CommonCompletionParams): AsyncGenerator<StreamedResponseChunk> {
	const { llm = "gemini-pro", messages, metricData, cacheIdx = 0 } = params;

	// Ensure metricData is provided for all LLM providers
	if (!metricData) {
		throw new Error("metricData is required for LLM streaming completions");
	}

	switch (llm) {
		case "chatgpt":
			yield* streamCompletionChatGpt({
				messages,
				metricData,
				cacheIdx,
			});
			break;

		case "gemini-pro":
		case "gemini-flash":
			yield* streamCompletionGemini({
				...transformMessages(messages),
				cacheIdx,
				metricData,
				model: GeminiVariantToModel[llm],
			});
			break;

		default:
			throw new Error(`Unsupported LLM provider: ${llm}`);
	}
}

/**
 * A unified structured completion function that works with multiple LLM providers.
 *
 * @param params The parameters for the structured completion
 * @returns A promise that resolves to the response with usage information
 */
export async function structuredCompletion<ZodInput extends z.ZodType, R = z.infer<ZodInput>>(
	params: { responseSchema: ZodInput } & CommonCompletionParams,
): Promise<ResponseWithUsage<R>> {
	const { llm = "gemini-pro", messages, metricData, cacheIdx = 0, responseSchema } = params;

	// Ensure metricData is provided for all LLM providers
	if (!metricData) {
		throw new Error("metricData is required for LLM structured completions");
	}

	switch (llm) {
		case "chatgpt":
			return await structuredCompletionChatGpt({
				zodObject: responseSchema,
				objectName: "result",
				messages,
				metricData,
				cacheIdx,
			});

		case "gemini-pro":
		case "gemini-flash":
			return await structuredCompletionGemini({
				responseSchema,
				...transformMessages(messages),
				cacheIdx,
				metricData,
				model: GeminiVariantToModel[llm],
			});

		default:
			throw new Error(`Unsupported LLM provider: ${llm}`);
	}
}

function transformMessages(simpleMessages: SimpleChatMessage[]) {
	const [systemInstruction, messages] =
		simpleMessages[0].role === "system" ? [simpleMessages[0].content, simpleMessages.slice(1)] : [undefined, simpleMessages];

	return { systemInstruction, contents: messages.map((m) => ({ role: m.role === "assistant" ? "model" : "user", text: m.content })) };
}
