export type PeriodType = "standard-period" | "1-week" | "calendar-months" | "quarterly";

export interface Period {
	periodType: PeriodType;
	periodSince: Date | number;
}

type DefaultPeriodType = {
	key: PeriodType;
	displayValue: string;
};

export const StandardPeriodLengthDays = 28;

export const isDatePeriod = (periodType: PeriodType) => periodType === "standard-period" || periodType === "1-week";

export const DefaultPeriods: DefaultPeriodType[] = [
	{ key: "standard-period", displayValue: "6 x 4-weeks" },
	{ key: "1-week", displayValue: "6 x 1-week" },
	{ key: "calendar-months", displayValue: "6 calendar months" },
	{ key: "quarterly", displayValue: "Quarterly" },
];

export function defaultPeriodsDynamic(periodLength: number): DefaultPeriodType[] {
	const standardPeriodLengthString = periodLengthString(periodLength);
	const standardPeriodDisplayValue = `6 x ${standardPeriodLengthString}`;
	return [
		{ key: "standard-period", displayValue: standardPeriodDisplayValue },
		{ key: "1-week", displayValue: "6 x 1-week" },
		{ key: "calendar-months", displayValue: "6 calendar months" },
		{ key: "quarterly", displayValue: "Quarterly" },
	];
}

export function periodLengthString(periodLength: number) {
	return periodLength !== 0 && periodLength % 7 === 0 ? `${periodLength / 7} weeks` : `${periodLength} days`;
}
