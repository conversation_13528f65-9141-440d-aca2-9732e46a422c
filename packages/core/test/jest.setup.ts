import { expect, jest } from "@jest/globals";
import path from "path";
import { PostgreSqlContainer, StartedPostgreSqlContainer } from "@testcontainers/postgresql";
import { promises as fs } from "fs";
import postgres from "postgres";
import type { EffReportSummaryResponseChunk } from "../src/eff-report/EffReportSummaryResponseChunk";
import { match, P } from "ts-pattern";
import logger from "../src/logger";
import { GetFarmInfo } from "../src/cf-link/GetFarmInfo";
import { GetCfLinkData } from "../src/cf-link/GetCfLinkData";

let pgContainer: StartedPostgreSqlContainer;

let testDBSql: postgres.Sql;

export const DefaultTestLength = 10 * 60000;

beforeAll(async () => {
	[pgContainer] = await Promise.all([new PostgreSqlContainer().withDatabase("pigbot_template").start()]);

	testDBSql = postgres({
		host: pgContainer.getHost(),
		port: pgContainer.getPort(),
		user: pgContainer.getUsername(),
		password: pgContainer.getPassword(),
		database: pgContainer.getDatabase(),
	});

	await migrate();
}, 60000);

async function migrate() {
	// Run Hasura migrations
	const hasuraDir = path.resolve(__dirname, "../../../hasura");

	const migrationsPath = path.join(hasuraDir, "migrations", "vfa");
	const directories = await fs.readdir(migrationsPath, { withFileTypes: true });

	// Filter and sort directories
	const sortedDirs = directories
		.filter((dirent) => dirent.isDirectory())
		.map((dirent) => dirent.name)
		.sort((a, b) => a.localeCompare(b, undefined, { numeric: true, sensitivity: "base" }));

	await testDBSql.begin(async (sql) => {
		// Run migrations
		for (const dir of sortedDirs) {
			const sqlFilePath = path.join(migrationsPath, dir, "up.sql");

			try {
				await sql.file(sqlFilePath);
			} catch (error) {
				logger.error(`Error applying migration ${dir}:`, error);
				throw error;
			}
		}
	});
}

async function clearRedisButKeepCompletionCache() {}

let idx = 0;

beforeEach(async () => {
	process.env.ENABLE_WIP = "true"; // By default, ENABLE_WIP is true for tests

	process.env.PG_HOST = pgContainer.getHost();
	process.env.PG_PORT = pgContainer.getPort().toString();
	process.env.PG_USER = pgContainer.getUsername();
	process.env.PG_PASSWORD = pgContainer.getPassword();

	const testDatabaseName = `test_${idx++}`;
	await testDBSql.unsafe(`CREATE DATABASE ${testDatabaseName} TEMPLATE ${pgContainer.getDatabase()};`);

	process.env.PG_DATABASE = testDatabaseName;
});

afterEach(async () => {
	await Promise.all([clearRedisButKeepCompletionCache()]);

	const { sharedRedisClient } = await import("../src/RedisClient");
	const { sql } = await import("../src/database");
	const { cachePromise } = await import("../src/Cache");

	// All clients must be stopped after test otherwise jest throws an error
	await Promise.all([(await cachePromise).store.client.quit(), sharedRedisClient.quit(), sql.end()]);

	// Reset all modules so that the next will read the new process.env values
	jest.resetModules();
	jest.resetAllMocks();
});

afterAll(async () => {
	await testDBSql.end();
	await Promise.all([pgContainer.stop()]);
}, 60000);

export async function parseResponseStream(response: AsyncGenerator<EffReportSummaryResponseChunk>) {
	let responseId: string | undefined = undefined;
	const responseArray: EffReportSummaryResponseChunk[] = [];

	for await (const chunk of response) {
		match(chunk)
			.with({ responseId: P.string }, (v) => (responseId = v.responseId))
			.otherwise(() => {});

		responseArray.push(chunk);
	}

	expect(responseId).toBeDefined();

	return { responseId: responseId!, responseArray };
}

export function mockTemporal() {
	// This is a mock implementation that returns basic data for running tests.
	// temporal module can be mocked manually if custom responses or validations are needed.
	const startOrAwaitWorkflow = jest.fn(
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		(workerQueue: string, workflowId: string, workflowFunction: (args: never) => Promise<never>, args: unknown) => {
			type ReturnTypeSync<T extends (...args: never) => unknown> = ReturnType<T> extends Promise<infer U> ? U : ReturnType<T>;

			return match(workflowFunction.name)
				.with(GetFarmInfo.name, () => {
					const response: ReturnTypeSync<typeof GetFarmInfo> = {
						kpiGoals: [],
						farmName: "farm foo",
						countryCode: "US",
						settings: [
							{
								label: "test",
								code: "TEST",
								description: "This is just for test purposes",
								value: "foo",
								unit: "bar",
							},
						],
						farmTimeline: [
							{
								from: "2025-01-01",
								description: "test",
							},
							{
								from: "2025-01-02",
								to: "2025-01-03",
								description: "test2",
							},
							{
								from: "2025-01-03",
								to: "2025-01-03",
								description: "testOneDay",
							},
						],
					};

					return response;
				})
				.with(GetCfLinkData.name, () => {
					const response: ReturnTypeSync<typeof GetCfLinkData> = {
						medicineUsages: [
							{
								illnessType: "test illness",
								medicine: "test medicine",
								animalType: "SOW",
								medicineUnit: "mg",
								amounts: [{ medicineAmount: 100, animalCount: 10 }, null],
							},
						],
						additionalKpis: [
							{
								name: "test",
								code: "TEST",
								description: "This is just for test purposes",
								values: [123, null],
							},
							{
								name: "test",
								code: "TEST",
								description: undefined,
								values: [123, null],
							},
						],
						pregnancyFailures: [],
					};
					return response;
				})
				.otherwise(() => {
					throw new Error(`Mocked temporal: Unexpected workflow function name: ${workflowFunction.name}`);
				});
		},
	);

	// It's not enough to mock just startOrAwaitWorkflow
	// temporal module initializes connection to temporal on import
	// and it crashes in tests when temporal is not running
	// so we need to mock the whole module.
	// This way no connection is attempted.
	jest.mock("../src/temporal/temporal", () => ({
		__esModule: true,
		startOrAwaitWorkflow,
	}));
	return startOrAwaitWorkflow;
}
