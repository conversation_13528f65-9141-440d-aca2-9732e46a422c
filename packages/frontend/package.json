{"name": "pigbot-frontend", "version": "2.0.0", "description": "Frontend for Virtual Farm Assistant", "scripts": {"start": "webpack serve --config webpack.config.js --mode development", "build": "webpack --config webpack.config.js --mode production", "serve": "webpack serve --config webpack.config.js --mode production --env SERVE=true", "lint": "eslint --ext .js,.jsx,.ts,.tsx src/", "codegen": "graphql-codegen --config codegen.ts"}, "devDependencies": {"@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.2", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.0.9", "@graphql-codegen/typescript-operations": "^4.2.3", "@graphql-codegen/typescript-react-apollo": "^4.3.0", "@marshallofsound/webpack-asset-relocator-loader": "^0.5.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.8", "@types/node": "^20.16.7", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/webpack-env": "^1.18.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "dotenv": "^16.4.5", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "12.2.0", "mini-css-extract-plugin": "^2.9.1", "react-refresh": "^0.14.0", "sass": "^1.77.8", "sass-loader": "^14.2.1", "serve": "^14.0.1", "style-loader": "^4.0.0", "swc-loader": "^0.2.6", "ts-loader": "9.5.1", "typescript": "^5.6.2", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "dependencies": {"@apollo/client": "^3.11.4", "@emotion/react": "11.11.4", "@graphql-typed-document-node/core": "^3.1.1", "@hookform/resolvers": "3.9.0", "@tanstack/react-query": "^5.56.2", "@trpc/client": "11.0.0-rc.700", "@trpc/react-query": "11.0.0-rc.700", "@types/jsrsasign": "^10.5.14", "antd": "3.26.18", "apollo-link-http": "^1.5.17", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "graphql": "^15.9.0", "graphql-ws": "^5.16.0", "jsrsasign": "^11.1.0", "jwt-decode": "^4.0.0", "mobx": "^6.13.1", "mobx-react-lite": "^4.0.7", "moment": "^2.30.1", "p-defer": "^4.0.1", "pigbot-backend": "workspace:*", "pigbot-core": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-json-view": "^1.21.3", "react-markdown": "9.0.1", "react-pdf": "^9.2.1", "react-router-dom": "^6.26.1", "react-shadow": "^20.4.0", "react-textarea-autosize": "^8.5.3", "react-use": "^17.5.1", "rehype-external-links": "^3.0.0", "remark-gfm": "^4.0.0", "simple-statistics": "^7.8.5", "ts-pattern": "^5.4.0", "turndown": "^7.2.0", "use-debounce": "^10.0.3", "zod": "^3.23.8"}}