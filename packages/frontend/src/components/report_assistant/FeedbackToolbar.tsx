/** @jsxImportSource @emotion/react */
import React from 'react';
import { css } from '@emotion/react';
import 'antd/lib/alert/style/index.less';
import Button from 'antd/lib/button';
import { observer } from 'mobx-react-lite';

interface FeedbackToolbarProps {
	goBack: () => void;
}

export const FeedbackToolbar: React.FC<FeedbackToolbarProps> = observer((props) => {
	return (
		<div
			css={css`
				display: flex;
				gap: 0rem 1rem;
				flex-wrap: wrap;
				justify-content: center;
				padding-bottom: 10px;
			`}
		>
			<hr
				css={css`
					flex-basis: 100%;
				`}
			/>
			<Button onClick={() => props.goBack()}>Close feedback</Button>
		</div>
	);
});
