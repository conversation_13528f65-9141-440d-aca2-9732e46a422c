/** @jsxImportSource @emotion/react */
import { makeAutoObservable } from 'mobx';
import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import PigbotMarkdown from '@src/components/common/PigbotMarkdown';
import { Section, splitMarkdownV3 } from 'pigbot-core/src/feedback/splitMarkdown';
import Button from 'antd/lib/button';
import Alert from 'antd/lib/alert';
import 'antd/lib/alert/style/index.less';
import { css } from '@emotion/react';
import TextareaAutosize from 'react-textarea-autosize';
import { ENTIRE_RESPONSE_KEY, GOOD_RESPONSE } from 'pigbot-core/src/Constants';
import { graphql } from '@src/gql';
import { useSubscription } from '@apollo/client';
import TrpcReact from '@src/common/TrpcReact';
import { AnalysisComponent } from '@src/components/report_assistant/AnalysisComponent';
import { AnalysisOpenedStates } from '@src/components/report_assistant/ReportAssistant';

export class FeedbackState {
	completion: string;
	splitAnalysis: Section[][];
	responseId: string;
	sectionFeedback: Record<string, string> = {};

	constructor(completion: string, analysis: string[], responseId: string) {
		this.completion = completion;
		this.responseId = responseId;
		this.splitAnalysis = analysis.map((a) => splitMarkdownV3(a));

		makeAutoObservable(this);
	}

	updateFeedback(feedback: Record<string, string> | undefined) {
		this.sectionFeedback = feedback || {};
	}

	setFeedback(sectionKey: string, feedback: string) {
		this.sectionFeedback[sectionKey] = feedback;
	}

	removeFeedback(sectionKey: string) {
		delete this.sectionFeedback[sectionKey];
	}

	get rootSections() {
		return splitMarkdownV3(this.completion, true);
	}
}

const GetFeedback = graphql(`
	subscription GetFeedback($responseId: String!) {
		response_feedback(where: { id: { _eq: $responseId } }) {
			feedback
		}
	}
`);

export const FeedbackMode: React.FC<{
	state: FeedbackState;
	analysisOpenedStates: AnalysisOpenedStates;
}> = observer(({ state, analysisOpenedStates }) => {
	type HoveredSection = {
		key: string;
		depth: number;
	};

	const { data: feedbackData } = useSubscription(GetFeedback, {
		variables: { responseId: state.responseId },
	});

	useEffect(() => {
		if (feedbackData?.response_feedback) {
			state.updateFeedback(feedbackData.response_feedback[0]?.feedback);
		}
	}, [feedbackData]);

	const saveFeedbackMutation = TrpcReact.saveFeedback.useMutation();
	const deleteFeedbackMutation = TrpcReact.deleteFeedback.useMutation();

	const [allHoveredSections, setAllHoveredSections] = useState<HoveredSection[]>([]);
	const [selectedSection, setSelectedSection] = useState<{
		key: string;
		feedback: string;
	} | null>(null);

	function selectSection(key: string) {
		setSelectedSection({ key: key, feedback: state.sectionFeedback[key] || '' });
	}

	const hoveredSection = allHoveredSections.reduce<HoveredSection | null>(
		(acc, curr) => (acc && acc.depth > curr.depth ? acc : curr),
		null,
	);

	// Reference for the text area used in edit mode
	const textAreaRef = useRef<HTMLTextAreaElement>(null);

	// Focus on the text area when in edit mode and set the cursor to the end
	useEffect(() => {
		if (textAreaRef.current) {
			textAreaRef.current.focus();
			const length = textAreaRef.current.value.length;
			textAreaRef.current.setSelectionRange(length, length);
		}
	}, [selectedSection]);

	function feedbackView(key: string, textPlaceholder: string) {
		const isSelected = selectedSection && selectedSection.key === key;
		const currentSectionFeedback = state.sectionFeedback[key];
		const isGoodResponse = currentSectionFeedback === GOOD_RESPONSE;

		function saveFeedback(feedback: string) {
			saveFeedbackMutation.mutate(
				{
					responseId: state.responseId,
					sectionId: key,
					feedback: feedback,
				},
				{
					onSuccess: () => {
						state.setFeedback(key, feedback);
						setSelectedSection(null);
					},
				},
			);
		}

		function deleteFeedback() {
			deleteFeedbackMutation.mutate(
				{
					responseId: state.responseId,
					sectionId: key,
				},
				{
					onSuccess: () => {
						state.removeFeedback(key);
						setSelectedSection(null);
					},
				},
			);
		}

		return (
			<>
				{(!isSelected || isGoodResponse) && currentSectionFeedback && (
					<Alert
						type={isGoodResponse ? 'success' : 'warning'}
						message={isGoodResponse ? 'Good response.' : currentSectionFeedback}
						css={css`
							margin-left: 35px;
							margin-bottom: 10px;
							white-space: break-spaces;
						`}
					/>
				)}

				{isSelected && (
					<div
						css={css`
							margin: 0.5rem;
						`}
					>
						{!isGoodResponse && (
							<TextareaAutosize
								ref={textAreaRef}
								value={selectedSection.feedback}
								placeholder={textPlaceholder}
								onChange={(e) =>
									setSelectedSection({
										...selectedSection,
										feedback: e.target.value,
									})
								}
								css={css`
									width: 100%;
									padding: 5px;
								`}
							/>
						)}
						<div
							css={css`
								display: flex;
								gap: 10px;
								padding: 10px 0;
								align-content: center;
							`}
						>
							{!isGoodResponse && (
								<Button
									type='primary'
									loading={saveFeedbackMutation.isPending}
									disabled={
										!selectedSection.feedback || selectedSection.feedback === currentSectionFeedback || saveFeedbackMutation.isPending
									}
									onClick={(e) => {
										e.stopPropagation();
										saveFeedback(selectedSection.feedback);
									}}
								>
									Save
								</Button>
							)}
							<Button
								onClick={(e) => {
									e.stopPropagation();
									setSelectedSection(null);
								}}
							>
								Cancel
							</Button>
							{currentSectionFeedback ? (
								<>
									<div
										css={css`
											flex: 1;
										`}
									/>
									<Button
										type='link'
										disabled={deleteFeedbackMutation.isPending}
										loading={deleteFeedbackMutation.isPending}
										onClick={(e) => {
											e.stopPropagation();
											deleteFeedback();
										}}
									>
										Delete
									</Button>
								</>
							) : (
								!selectedSection.feedback && (
									<Button
										icon='like'
										onClick={(e) => {
											e.stopPropagation();
											saveFeedback(GOOD_RESPONSE);
										}}
									>
										Good response
									</Button>
								)
							)}
						</div>
						{saveFeedbackMutation.error && <Alert message={saveFeedbackMutation.error.message} type='error' showIcon />}
						{deleteFeedbackMutation.error && <Alert message={deleteFeedbackMutation.error.message} type='error' showIcon />}
					</div>
				)}
			</>
		);
	}

	function renderSections(sections: Section[], depth: number = 0, parentKey?: string) {
		return (
			<>
				{sections.map((section, index) => {
					const sectionId = parentKey ? `${parentKey}-${index}` : `${index}`;
					return renderSection(section, depth, sectionId);
				})}
			</>
		);
	}

	function renderSection(section: Section, depth: number, sectionId: string) {
		const isHovered = hoveredSection && hoveredSection.key === sectionId;
		const isSelected = selectedSection && selectedSection.key === sectionId;

		return (
			<div
				key={sectionId}
				style={{
					paddingLeft: `${depth * 20}px`,
					backgroundColor: isSelected ? '#e6f7ff' : isHovered ? '#ebebeb' : 'transparent',
				}}
				onMouseEnter={() => setAllHoveredSections((current) => [...current, { key: sectionId, depth }])}
				onMouseLeave={() => setAllHoveredSections((current) => current.filter((s) => s.key !== sectionId))}
				onClick={(e) => {
					e.stopPropagation();
					if (!isSelected) selectSection(sectionId);
				}}
				title={!selectedSection ? 'Click to add feedback' : undefined}
				css={css`
					cursor: pointer;
				`}
			>
				<PigbotMarkdown>{section.content}</PigbotMarkdown>

				{renderSections(section.children, depth + 1, sectionId)}

				{feedbackView(sectionId, 'How can this section be improved?')}
			</div>
		);
	}

	return (
		<>
			<div>
				{state.rootSections.map((sec, index) => {
					return (
						<div key={index}>
							{renderSection(sec, 0, `${index}`)}
							<AnalysisComponent
								analysisComponent={renderSections(state.splitAnalysis[index], 0, `A-${index}`)}
								isOpened={() => analysisOpenedStates.isAnalysisOpen(index)}
								toggleOpened={() => analysisOpenedStates.toggleAnalysis(index)}
							/>
							<hr />
						</div>
					);
				})}

				<div
					css={css`
						width: 100%;
						display: flex;
						flex-direction: column;
					`}
					style={{ cursor: selectedSection?.key !== ENTIRE_RESPONSE_KEY ? 'pointer' : 'default' }}
					onClick={() => selectSection(ENTIRE_RESPONSE_KEY)}
				>
					{selectedSection?.key === ENTIRE_RESPONSE_KEY || state.sectionFeedback[ENTIRE_RESPONSE_KEY] ? (
						feedbackView(ENTIRE_RESPONSE_KEY, 'Summarize the feedback on the entire response.')
					) : (
						/* The button is visible if not editing feedback for entire-response, or there is no entire-response feedback yet. */
						<Button size='default' type='dashed'>
							Give feedback on the entire assistant response.
						</Button>
					)}
				</div>
			</div>
		</>
	);
});
