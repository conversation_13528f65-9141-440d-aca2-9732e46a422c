import { css } from '@emotion/react';

export default css`
	// Markdown fixes

	p {
		margin: 0 !important;
	}

	/* Reset lists */

	ul,
	ol {
		padding-left: 40px !important;
		margin: 1em 0 !important;
	}

	/* Reset headings */

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		font-weight: bold !important;
		margin: 1em 0 !important;
		padding: 0 !important;
	}

	h1 {
		font-size: 2em !important;
	}

	h2 {
		font-size: 1.5em !important;
	}

	h3 {
		font-size: 1.17em !important;
	}

	h4 {
		font-size: 1em !important;
	}

	h5 {
		font-size: 0.83em !important;
	}

	h6 {
		font-size: 0.67em !important;
	}

	/* Reset paragraphs */

	p {
		margin: 1em 0 !important;
	}

	/* Reset links */

	a {
		color: #0000ee !important;
		text-decoration: underline !important;
	}

	a:visited {
		color: #551a8b !important;
	}

	table,
	th,
	td {
		font-size: 1em !important;
	}

	// Table style

	table {
		width: 100%;
		border-collapse: collapse;
		font-family: Arial, sans-serif;
		font-size: 14px;
		text-align: left;
	}

	th,
	td {
		border: 1px solid #ddd;
		padding: 2px 4px;
		vertical-align: middle;
	}

	th {
		background-color: #f0f0f0ff;
		font-weight: bold;
	}

	tr:nth-of-type(even) {
		background-color: #f0f0f0;
	}

	tr:hover {
		background-color: #f1f1f1;
	}

	thead {
		background-color: #e9e9e9;
		border-bottom: 2px solid #ddd;
	}
`;
