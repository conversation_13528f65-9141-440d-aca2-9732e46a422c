/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';

class EChartErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
	constructor(props: { children: React.ReactNode }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError() {
		return { hasError: true };
	}

	render() {
		if (this.state.hasError) {
			return <div className='echart-error'>Error rendering chart.</div>;
		}

		return this.props.children;
	}
}

const ChartComponent = ({ option, height }: { option: unknown; height: number }) => {
	// Basic validation of required chart options
	if (!option || typeof option !== 'object') {
		throw new Error('Invalid chart options');
	}

	return <ReactECharts option={option} style={{ height: `${height}px` }} />;
};

type EChartRendererProps = {
	value: {
		title: string;
		option: EChartsOption;
		explanation: string;
		height: number;
	};
};

const EChartRenderer: React.FC<EChartRendererProps> = ({ value }) => {
	try {
		const { option, explanation } = value;
		if (!option || typeof option !== 'object') {
			return <div className='echart-error'>Invalid chart configuration.</div>;
		}

		return (
			<div
				css={css`
					margin-bottom: 30px;
					margin-top: 30px;
				`}
			>
				<EChartErrorBoundary>
					<ChartComponent option={option} height={value.height} />
				</EChartErrorBoundary>
				{explanation && <i>{explanation}</i>}
			</div>
		);
	} catch (error) {
		return <div className='echart-error'>Error parsing chart data.</div>;
	}
};

export default EChartRenderer;
