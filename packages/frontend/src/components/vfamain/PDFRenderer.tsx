/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import TrpcReact from '@src/common/TrpcReact';
import { TRPCClientErrorLike } from '@trpc/client';
import Button from 'antd/lib/button';
import Spin from 'antd/lib/spin';
import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { LoadingError } from '../common/LoadingError';

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

export class PDFRendererState {
	constructor(
		public documentName: string,
		public pageNumber: number,
	) {
		makeAutoObservable(this);
	}

	openLink(documentName: string, pageNumber: number) {
		this.documentName = documentName;
		this.pageNumber = pageNumber;
	}

	nextPage() {
		this.pageNumber++;
	}

	previousPage() {
		this.pageNumber--;
	}
}

interface PDFRendererProps {
	state: PDFRendererState;
	onClose: () => void;
}

export const PDFRenderer: React.FC<PDFRendererProps> = observer((props) => {
	const pdfDataQuery = TrpcReact.getPdfData.useQuery(props.state.documentName, {
		retry: false,
	});

	// TODO numPages should be cleared when documentName changes - the best way to do it is to move it to the State
	const [numPages, setNumPages] = useState<number | null>(null);

	const loadingSpin = (
		<div
			css={css`
				display: flex;
				flex: 1;
				justify-content: center;
				align-items: center;
			`}
		>
			<Spin />
		</div>
	);

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const errorMessage = (error: TRPCClientErrorLike<any> | string) => (
		<div
			css={css`
				display: flex;
				flex: 1;
				justify-content: center;
				align-items: center;
			`}
		>
			<LoadingError error={error} />
		</div>
	);

	return (
		<>
			{pdfDataQuery.isLoading && loadingSpin}

			{pdfDataQuery.error !== null && errorMessage(pdfDataQuery.error)}

			{pdfDataQuery.data && (
				<div
					css={css`
						display: flex;
						flex: 1;
						flex-direction: row;
						overflow: hidden;
						align-items: stretch;
						justify-content: center;
						padding: 0 10px;
					`}
				>
					{!pdfDataQuery.isLoading && (
						<div
							css={css`
								display: flex;
								align-items: center;
							`}
						>
							<Button disabled={props.state.pageNumber <= 1} icon='left' onClick={() => props.state.previousPage()} />
						</div>
					)}

					<div
						css={css`
							display: flex;
							flex: 1;
							align-items: center;
							justify-content: center;
							overflow: auto;
						`}
					>
						<Document
							file={`data:application/pdf;base64,${pdfDataQuery.data}`}
							loading={loadingSpin}
							onLoadSuccess={(doc) => setNumPages(doc.numPages)}
							onLoadError={(error) => errorMessage(error.message)}
						>
							<Page pageNumber={props.state.pageNumber} />
						</Document>
					</div>

					{!pdfDataQuery.isLoading && (
						<div
							css={css`
								display: flex;
								position: relative;
							`}
						>
							<Button
								css={css`
									position: absolute;
									z-index: 1;
								`}
								icon='close'
								onClick={() => props.onClose()}
							/>
							<div
								css={css`
									display: flex;
									flex: 1;
									align-items: center;
								`}
							>
								<Button disabled={!!numPages && props.state.pageNumber >= numPages} icon='right' onClick={() => props.state.nextPage()} />
							</div>
						</div>
					)}
				</div>
			)}
		</>
	);
});

/*

To load PDF content stored in the source_doc table in PostgreSQL and render it in your PDFRenderer
component, without saving a temporary file to disk, the best approach would be to stream the data or
lazy-load the data directly from the database into the frontend, which avoids keeping the entire PDF
in memory and doesn't require saving the file to disk.

Here's how you can implement this in a way that is both memory-efficient and scalable.
Step-by-Step Plan:

    Backend (tRPC):
        Use streaming or lazy loading to handle the file_data (which is a bytea column).
        Instead of sending the entire file_data at once, you can send the data in chunks to the frontend.
        Base64 Encoding: You can send the file_data as a base64-encoded string to make it easy for the frontend
                         to handle (or use binary streams, but base64 is more common for small to medium-size PDFs).

    Frontend (React + PDFRenderer):
        Use the base64-encoded string to load the PDF in the react-pdf component.
        You can use lazy loading to render the PDF page by page as it loads, avoiding loading the entire document at once.

Key Considerations:

    Base64 Encoding:
        The file_data from PostgreSQL is a bytea, which is binary. You can convert it into a base64 string to send it safely over HTTP.
        The frontend uses a data URL (data:application/pdf;base64,...) to render the PDF.

    Trpc Lazy Loading:
        react-pdf handles rendering the document page by page, so you're effectively lazy loading the pages as they are requested.
        By passing the base64-encoded string as a data: URL, the browser handles it efficiently and renders the PDF directly.

    Memory Management:
        By not storing the entire PDF in memory and instead passing a base64 string (which is much smaller than the binary data in memory),
        you reduce the memory footprint.
        React-pdf loads pages on demand, further improving memory usage since only the currently viewed page is rendered.

*/
