/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useMemo } from 'react';
import TrpcReact from '@src/common/TrpcReact';
import Spin from 'antd/lib/spin';
import 'antd/lib/spin/style/index.less';
import { LoadingError } from '@src/components/common/LoadingError';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import { EffReportSummaryView } from '../report_assistant/EffReportSummaryView';
import { AnalysisOpenedStates } from '../report_assistant/ReportAssistant';
import { FeedbackMode, FeedbackState } from '../report_assistant/FeedbackMode';
import { ReportToolbar } from '../report_assistant/ReportToolbar';
import { EffReportSummaryViewerState } from '../report_assistant/EffReportSummaryViewerState';
import { FeedbackToolbar } from '../report_assistant/FeedbackToolbar';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { Period } from 'pigbot-core/src/vfamain/Periods';
import { PeriodInterval } from './PeriodInterval';

export class FarmAnalysisState {
	viewState: EffReportSummaryViewerState = new EffReportSummaryViewerState();
	feedbackStates: { [key: string]: FeedbackState } = {};
	editingFeedback: boolean = false;
	openedAnalysis: AnalysisOpenedStates = new AnalysisOpenedStates();

	constructor() {
		makeAutoObservable(this);
	}

	editFeedback(feedback: FeedbackState) {
		if (this.feedbackStates[feedback.responseId] === undefined) this.feedbackStates[feedback.responseId] = feedback;
		this.editingFeedback = true;
	}

	stopFeedback() {
		this.editingFeedback = false;
	}
}

interface FarmAnalysisProps {
	state: FarmAnalysisState;
	farmLocationId: number | null;
	period: Period;
	periodInterval: PeriodInterval;
	openLink: (pdfDocumentName: string, pageNumber: number) => void;
	setAnalysisRelevantData: (index: number, title: string, relevantData: string) => void;
}

export const FarmAnalysis: React.FC<FarmAnalysisProps> = observer((props) => {
	const efficiencyReportSummaryResponse = TrpcReact.getLatestEfficiencyReportSummary.useQuery(
		{
			farmLocationId: props.farmLocationId,
			idx: props.state.viewState.completionIdx,
			period: props.period,
		},
		{ retry: false },
	);
	const responseData = useMemo(
		() => extractDataFromEffReportAnalysisArray(efficiencyReportSummaryResponse.data ?? []),
		[efficiencyReportSummaryResponse.data],
	);

	const showFeedbackMode =
		responseData.responseId && props.state.feedbackStates[responseData.responseId] !== undefined && props.state.editingFeedback;
	const showSummaryView = !showFeedbackMode && efficiencyReportSummaryResponse.error === null && responseData;
	const showEfficiencyReportSummary = efficiencyReportSummaryResponse.error === null && responseData;

	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				flex: 1;
				gap: 10px;
				overflow-y: auto;
			`}
		>
			<>
				{efficiencyReportSummaryResponse.isLoading && (
					<div
						css={css`
							justify-content: center;
							flex: 1;
							display: flex;
							align-items: center;
						`}
					>
						<Spin />
					</div>
				)}

				{efficiencyReportSummaryResponse.error !== null && (
					<div
						css={css`
							justify-content: center;
							flex: 1;
							display: flex;
							align-items: center;
						`}
					>
						<LoadingError error={efficiencyReportSummaryResponse.error} />
					</div>
				)}

				{/* Efficiency report summary with feedback mode */}
				{showEfficiencyReportSummary && (
					<>
						<div
							css={css`
								display: flex;
								flex: 1;
								flex-direction: column;
								overflow-y: auto;
								padding: 0 25px;
							`}
						>
							<h3>{`Period ${props.periodInterval.to.format('DD.MM.YYYY')} - ${props.periodInterval.from.format('DD.MM.YYYY')}`}</h3>

							{showFeedbackMode && responseData.responseId && (
								<FeedbackMode
									state={props.state.feedbackStates[responseData.responseId]}
									analysisOpenedStates={props.state.openedAnalysis}
								/>
							)}

							{showSummaryView && (
								<EffReportSummaryView
									data={responseData}
									state={props.state.viewState}
									internalToolsVisible={false}
									analysisOpenedStates={props.state.openedAnalysis}
									handleSOPLinkClick={(documentName, pageNumber) => {
										console.log(`Open file "${documentName}" at page ${pageNumber}`);
										props.openLink(documentName, Number.parseInt(pageNumber));
									}}
									handleIssueMouseOver={(index, title, relevantData) => props.setAnalysisRelevantData(index, title, relevantData)}
								/>
							)}
						</div>

						{/* Toolbars at the bottom */}
						{showFeedbackMode && responseData.responseId && (
							<div
								css={css`
									flex-shrink: 0;
								`}
							>
								<FeedbackToolbar goBack={() => props.state.stopFeedback()} />
							</div>
						)}

						{showSummaryView && (
							<div
								css={css`
									flex-shrink: 0;
								`}
							>
								<ReportToolbar
									data={responseData}
									editFeedback={(feedbackState) => props.state.editFeedback(feedbackState)}
									viewState={props.state.viewState}
									disableButtons={responseData.responseId == null}
									displayRegenerateButton={false}
								/>
							</div>
						)}
					</>
				)}
			</>
		</div>
	);
});
