import { PeriodType } from 'pigbot-core/src/vfamain/Periods';

export type PeriodInterval = {
	from: moment.Moment;
	to: moment.Moment;
};

export function getPeriodInterval(periodType: PeriodType, periodSince: moment.Moment, periodLength: number): PeriodInterval {
	const from = periodSince;
	const to = resolveTo(periodType, periodSince, periodLength);

	return { from, to };
}

function resolveTo(periodType: PeriodType, periodSince: moment.Moment, periodLength: number): moment.Moment {
	switch (periodType) {
		case 'standard-period':
			return periodSince.clone().subtract(6 * periodLength, 'days');
		case '1-week':
			return periodSince.clone().subtract(6 * 7, 'days');
		case 'calendar-months':
			return periodSince.clone().subtract(6 * 1, 'months');
		case 'quarterly':
			return periodSince.clone().subtract(4 * 1, 'quarters');
	}
}
