/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import Alert from 'antd/lib/alert';
import RadioGroup from 'antd/lib/radio/group';
import RadioButton from 'antd/lib/radio/radioButton';
import { SowFarmLocations } from 'pigbot-backend/src/vfamain/VFAMainService';
import React from 'react';
import { Pending } from '../Pending';

interface FarmLocationSelectorProps {
	sowFarmLocations: SowFarmLocations;
	selectedFarmLocationId: number | null | Pending;
	onSelectedFarmLocationId: (farmLocationId: number | null) => void;
}

export const FarmLocationSelector: React.FC<FarmLocationSelectorProps> = (props) => {
	const isFarmSelected = props.selectedFarmLocationId !== Pending;
	const containsOneFarmOnly = props.sowFarmLocations && props.sowFarmLocations.length === 1;

	const farmSelector = (
		<div
			css={css`
				display: flex;
				justify-content: center;
			`}
		>
			{props.sowFarmLocations && (
				<RadioGroup buttonStyle='solid' onChange={(e) => props.onSelectedFarmLocationId(Number.parseInt(e.target.value))}>
					{props.sowFarmLocations.map((sowFarmLocation) => (
						<RadioButton key={sowFarmLocation.id} value={sowFarmLocation.id.toString()}>
							{sowFarmLocation.name}
						</RadioButton>
					))}
				</RadioGroup>
			)}
		</div>
	);

	const selectFarmToAnalyzeInfo = (
		<Alert
			css={css`
				margin: 30px 30px 10px 30px;
			`}
			message='Select farm to analyze.'
			type='warning'
		/>
	);

	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				justify-content: center;
			`}
		>
			{/* Display farm selector buttons only when there's more than 1 farm. */}
			{props.sowFarmLocations && !containsOneFarmOnly && farmSelector}

			{/* Display select farm info only when there's more than 1 farm */}
			{props.sowFarmLocations && !containsOneFarmOnly && !isFarmSelected && selectFarmToAnalyzeInfo}
		</div>
	);
};
