import { Worker } from "@temporalio/worker";
import { getTemporalNativeConnection } from "pigbot-core/src/temporal/temporal";
import Config from "pigbot-core/src/Config";

export async function runWorker() {
	const connection = await getTemporalNativeConnection();

	const workflowOption = () =>
		process.env.NODE_ENV === "production"
			? {
					workflowBundle: {
						codePath: require.resolve("../workflow-bundle.js"),
					},
				}
			: { workflowsPath: require.resolve("./workflows") };

	// Step 2: Register Workflows and Activities with the Worker.
	const worker = await Worker.create({
		connection,
		namespace: Config.TEMPORAL_NAMESPACE,
		taskQueue: "static-pigbot",
		...workflowOption(),
		bundlerOptions: {
			ignoreModules: ["path", "fs", "os", "crypto"],
		},
	});

	await worker.run();
}
